{"version": 3, "sources": ["../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, HostBinding, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n/**\n * Stack elements horizontally or vertically using this helper directive. Get started with importing the module:\n *\n * ```typescript\n * import { LayoutModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/layout-stack--basic)\n */\nclass StackDirective {\n  constructor(render, hostElement) {\n    this.render = render;\n    this.hostElement = hostElement;\n    /**\n     * Orientation of the items in the stack, defaults to `vertical`\n     * Empty string is equivalent to \"vertical\"\n     *\n     * Empty string has been added as an option for Angular 16+ to resolve type errors\n     */\n    this.cdsStack = \"vertical\";\n  }\n  get isHorizontal() {\n    return this.cdsStack === \"horizontal\";\n  }\n  get isVertical() {\n    return this.cdsStack === \"vertical\" || !this.cdsStack;\n  }\n  /**\n   * @deprecated as of v5 - Use `cdsStack` input property instead\n   */\n  set ibmStack(type) {\n    this.cdsStack = type;\n  }\n  /**\n   * Gap in the layout, provide a custom value (string) or a step from the spacing scale (number)\n   */\n  set gap(num) {\n    if (num !== undefined) {\n      this.render.removeClass(this.hostElement.nativeElement, `cds--stack-scale-${this._gap}`);\n      this.render.addClass(this.hostElement.nativeElement, `cds--stack-scale-${num}`);\n      this._gap = num;\n    }\n  }\n}\nStackDirective.ɵfac = function StackDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || StackDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nStackDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: StackDirective,\n  selectors: [[\"\", \"cdsStack\", \"\"], [\"\", \"ibmStack\", \"\"]],\n  hostVars: 4,\n  hostBindings: function StackDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--stack-horizontal\", ctx.isHorizontal)(\"cds--stack-vertical\", ctx.isVertical);\n    }\n  },\n  inputs: {\n    ibmStack: \"ibmStack\",\n    cdsStack: \"cdsStack\",\n    gap: \"gap\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StackDirective, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsStack], [ibmStack]\"\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    isHorizontal: [{\n      type: HostBinding,\n      args: [\"class.cds--stack-horizontal\"]\n    }],\n    isVertical: [{\n      type: HostBinding,\n      args: [\"class.cds--stack-vertical\"]\n    }],\n    ibmStack: [{\n      type: Input\n    }],\n    cdsStack: [{\n      type: Input\n    }],\n    gap: [{\n      type: Input\n    }]\n  });\n})();\nclass LayoutModule {}\nLayoutModule.ɵfac = function LayoutModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || LayoutModule)();\n};\nLayoutModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LayoutModule,\n  declarations: [StackDirective],\n  imports: [CommonModule],\n  exports: [StackDirective]\n});\nLayoutModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [StackDirective],\n      exports: [StackDirective],\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LayoutModule, StackDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAaA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,QAAQ,aAAa;AAC/B,SAAK,SAAS;AACd,SAAK,cAAc;AAOnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,cAAc,CAAC,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS,MAAM;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,IAAI,KAAK;AACX,QAAI,QAAQ,QAAW;AACrB,WAAK,OAAO,YAAY,KAAK,YAAY,eAAe,oBAAoB,KAAK,IAAI,EAAE;AACvF,WAAK,OAAO,SAAS,KAAK,YAAY,eAAe,oBAAoB,GAAG,EAAE;AAC9E,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAmB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAC1H;AACA,eAAe,OAAyB,kBAAkB;AAAA,EACxD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,EACtD,UAAU;AAAA,EACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,yBAAyB,IAAI,YAAY,EAAE,uBAAuB,IAAI,UAAU;AAAA,IACjG;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,KAAK;AAAA,EACP;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAmB;AAAC;AACpB,aAAa,OAAO,SAAS,qBAAqB,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,cAAc;AACjD;AACA,aAAa,OAAyB,iBAAiB;AAAA,EACrD,MAAM;AAAA,EACN,cAAc,CAAC,cAAc;AAAA,EAC7B,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,cAAc;AAC1B,CAAC;AACD,aAAa,OAAyB,iBAAiB;AAAA,EACrD,SAAS,CAAC,YAAY;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,cAAc;AAAA,MAC7B,SAAS,CAAC,cAAc;AAAA,MACxB,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}