{"version": 3, "sources": ["../../../../../../node_modules/xhr2/lib/xhr2.js"], "sourcesContent": ["// Generated by CoffeeScript 2.5.1\n(function() {\n  // This file's name is set up in such a way that it will always show up first in\n  // the list of files given to coffee --join, so that the other files can assume\n  // that XMLHttpRequestEventTarget was already defined.\n\n  // The DOM EventTarget subclass used by XMLHttpRequest.\n\n  // @see http://xhr.spec.whatwg.org/#interface-xmlhttprequest\n  var InvalidStateError, NetworkError, ProgressEvent, SecurityError, SyntaxError, XMLHttpRequest, XMLHttpRequestEventTarget, XMLHttpRequestUpload, http, https, os, url;\n\n  XMLHttpRequestEventTarget = (function() {\n    class XMLHttpRequestEventTarget {\n      // @private\n      // This is an abstract class and should not be instantiated directly.\n      constructor() {\n        this.onloadstart = null;\n        this.onprogress = null;\n        this.onabort = null;\n        this.onerror = null;\n        this.onload = null;\n        this.ontimeout = null;\n        this.onloadend = null;\n        this._listeners = {};\n      }\n\n      // Adds a new-style listener for one of the XHR events.\n\n      // @see http://www.w3.org/TR/XMLHttpRequest/#events\n\n      // @param {String} eventType an XHR event type, such as 'readystatechange'\n      // @param {function(ProgressEvent)} listener function that will be called when\n      //   the event fires\n      // @return {undefined} undefined\n      addEventListener(eventType, listener) {\n        var base;\n        eventType = eventType.toLowerCase();\n        (base = this._listeners)[eventType] || (base[eventType] = []);\n        this._listeners[eventType].push(listener);\n        return void 0;\n      }\n\n      // Removes an event listener added by calling addEventListener.\n\n      // @param {String} eventType an XHR event type, such as 'readystatechange'\n      // @param {function(ProgressEvent)} listener the value passed in a previous\n      //   call to addEventListener.\n      // @return {undefined} undefined\n      removeEventListener(eventType, listener) {\n        var index;\n        eventType = eventType.toLowerCase();\n        if (this._listeners[eventType]) {\n          index = this._listeners[eventType].indexOf(listener);\n          if (index !== -1) {\n            this._listeners[eventType].splice(index, 1);\n          }\n        }\n        return void 0;\n      }\n\n      // Calls all the listeners for an event.\n\n      // @param {ProgressEvent} event the event to be dispatched\n      // @return {undefined} undefined\n      dispatchEvent(event) {\n        var eventType, j, len, listener, listeners;\n        event.currentTarget = event.target = this;\n        eventType = event.type;\n        if (listeners = this._listeners[eventType]) {\n          for (j = 0, len = listeners.length; j < len; j++) {\n            listener = listeners[j];\n            listener.call(this, event);\n          }\n        }\n        if (listener = this[`on${eventType}`]) {\n          listener.call(this, event);\n        }\n        return void 0;\n      }\n\n    };\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'loadstart' event\n    XMLHttpRequestEventTarget.prototype.onloadstart = null;\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'progress' event\n    XMLHttpRequestEventTarget.prototype.onprogress = null;\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'abort' event\n    XMLHttpRequestEventTarget.prototype.onabort = null;\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'error' event\n    XMLHttpRequestEventTarget.prototype.onerror = null;\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'load' event\n    XMLHttpRequestEventTarget.prototype.onload = null;\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'timeout' event\n    XMLHttpRequestEventTarget.prototype.ontimeout = null;\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler\n    //   for the 'loadend' event\n    XMLHttpRequestEventTarget.prototype.onloadend = null;\n\n    return XMLHttpRequestEventTarget;\n\n  }).call(this);\n\n  // This file's name is set up in such a way that it will always show up second\n  // in the list of files given to coffee --join, so it can use the\n  // XMLHttpRequestEventTarget definition and so that the other files can assume\n  // that XMLHttpRequest was already defined.\n  http = require('http');\n\n  https = require('https');\n\n  os = require('os');\n\n  url = require('url');\n\n  XMLHttpRequest = (function() {\n    // The ECMAScript HTTP API.\n\n    // @see http://www.w3.org/TR/XMLHttpRequest/#introduction\n    class XMLHttpRequest extends XMLHttpRequestEventTarget {\n      // Creates a new request.\n\n      // @param {Object} options one or more of the options below\n      // @option options {Boolean} anon if true, the request's anonymous flag\n      //   will be set\n      // @see http://www.w3.org/TR/XMLHttpRequest/#constructors\n      // @see http://www.w3.org/TR/XMLHttpRequest/#anonymous-flag\n      constructor(options) {\n        super();\n        this.onreadystatechange = null;\n        this._anonymous = options && options.anon;\n        this.readyState = XMLHttpRequest.UNSENT;\n        this.response = null;\n        this.responseText = '';\n        this.responseType = '';\n        this.responseURL = '';\n        this.status = 0;\n        this.statusText = '';\n        this.timeout = 0;\n        this.upload = new XMLHttpRequestUpload(this);\n        this._method = null; // String\n        this._url = null; // Return value of url.parse()\n        this._sync = false;\n        this._headers = null; // Object<String, String>\n        this._loweredHeaders = null; // Object<lowercase String, String>\n        this._mimeOverride = null;\n        this._request = null; // http.ClientRequest\n        this._response = null; // http.ClientResponse\n        this._responseParts = null; // Array<Buffer, String>\n        this._responseHeaders = null; // Object<lowercase String, String>\n        this._aborting = null;\n        this._error = null;\n        this._loadedBytes = 0;\n        this._totalBytes = 0;\n        this._lengthComputable = false;\n      }\n\n      // Sets the XHR's method, URL, synchronous flag, and authentication params.\n\n      // @param {String} method the HTTP method to be used\n      // @param {String} url the URL that the request will be made to\n      // @param {?Boolean} async if false, the XHR should be processed\n      //   synchronously; true by default\n      // @param {?String} user the user credential to be used in HTTP basic\n      //   authentication\n      // @param {?String} password the password credential to be used in HTTP basic\n      //   authentication\n      // @return {undefined} undefined\n      // @throw {SecurityError} method is not one of the allowed methods\n      // @throw {SyntaxError} urlString is not a valid URL\n      // @throw {Error} the URL contains an unsupported protocol; the supported\n      //   protocols are file, http and https\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-open()-method\n      open(method, url, async, user, password) {\n        var xhrUrl;\n        method = method.toUpperCase();\n        if (method in this._restrictedMethods) {\n          throw new SecurityError(`HTTP method ${method} is not allowed in XHR`);\n        }\n        xhrUrl = this._parseUrl(url);\n        if (async === void 0) {\n          async = true;\n        }\n        switch (this.readyState) {\n          case XMLHttpRequest.UNSENT:\n          case XMLHttpRequest.OPENED:\n          case XMLHttpRequest.DONE:\n            // Nothing to do here.\n            null;\n            break;\n          case XMLHttpRequest.HEADERS_RECEIVED:\n          case XMLHttpRequest.LOADING:\n            // TODO(pwnall): terminate abort(), terminate send()\n            null;\n        }\n        this._method = method;\n        this._url = xhrUrl;\n        this._sync = !async;\n        this._headers = {};\n        this._loweredHeaders = {};\n        this._mimeOverride = null;\n        this._setReadyState(XMLHttpRequest.OPENED);\n        this._request = null;\n        this._response = null;\n        this.status = 0;\n        this.statusText = '';\n        this._responseParts = [];\n        this._responseHeaders = null;\n        this._loadedBytes = 0;\n        this._totalBytes = 0;\n        this._lengthComputable = false;\n        return void 0;\n      }\n\n      // Appends a header to the list of author request headers.\n\n      // @param {String} name the HTTP header name\n      // @param {String} value the HTTP header value\n      // @return {undefined} undefined\n      // @throw {InvalidStateError} readyState is not OPENED\n      // @throw {SyntaxError} name is not a valid HTTP header name or value is not\n      //   a valid HTTP header value\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-setrequestheader()-method\n      setRequestHeader(name, value) {\n        var loweredName;\n        if (this.readyState !== XMLHttpRequest.OPENED) {\n          throw new InvalidStateError(\"XHR readyState must be OPENED\");\n        }\n        loweredName = name.toLowerCase();\n        if (this._restrictedHeaders[loweredName] || /^sec\\-/.test(loweredName) || /^proxy-/.test(loweredName)) {\n          console.warn(`Refused to set unsafe header \\\"${name}\\\"`);\n          return void 0;\n        }\n        value = value.toString();\n        if (loweredName in this._loweredHeaders) {\n          // Combine value with the existing header value.\n          name = this._loweredHeaders[loweredName];\n          this._headers[name] = this._headers[name] + ', ' + value;\n        } else {\n          // New header.\n          this._loweredHeaders[loweredName] = name;\n          this._headers[name] = value;\n        }\n        return void 0;\n      }\n\n      // Initiates the request.\n\n      // @param {?String, ?ArrayBufferView} data the data to be sent; ignored for\n      //   GET and HEAD requests\n      // @return {undefined} undefined\n      // @throw {InvalidStateError} readyState is not OPENED\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-send()-method\n      send(data) {\n        if (this.readyState !== XMLHttpRequest.OPENED) {\n          throw new InvalidStateError(\"XHR readyState must be OPENED\");\n        }\n        if (this._request) {\n          throw new InvalidStateError(\"send() already called\");\n        }\n        switch (this._url.protocol) {\n          case 'file:':\n            this._sendFile(data);\n            break;\n          case 'http:':\n          case 'https:':\n            this._sendHttp(data);\n            break;\n          default:\n            throw new NetworkError(`Unsupported protocol ${this._url.protocol}`);\n        }\n        return void 0;\n      }\n\n      // Cancels the network activity performed by this request.\n\n      // @return {undefined} undefined\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-abort()-method\n      abort() {\n        if (!this._request) {\n          return;\n        }\n        this._request.abort();\n        this._setError();\n        this._dispatchProgress('abort');\n        this._dispatchProgress('loadend');\n        return void 0;\n      }\n\n      // Returns a header value in the HTTP response for this XHR.\n\n      // @param {String} name case-insensitive HTTP header name\n      // @return {?String} value the value of the header whose name matches the\n      //   given name, or null if there is no such header\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-getresponseheader()-method\n      getResponseHeader(name) {\n        var loweredName;\n        if (!this._responseHeaders) {\n          return null;\n        }\n        loweredName = name.toLowerCase();\n        if (loweredName in this._responseHeaders) {\n          return this._responseHeaders[loweredName];\n        } else {\n          return null;\n        }\n      }\n\n      // Returns all the HTTP headers in this XHR's response.\n\n      // @return {String} header lines separated by CR LF, where each header line\n      //   has the name and value separated by a \": \" (colon, space); the empty\n      //   string is returned if the headers are not available\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-getallresponseheaders()-method\n      getAllResponseHeaders() {\n        var lines, name, value;\n        if (!this._responseHeaders) {\n          return '';\n        }\n        lines = (function() {\n          var ref, results;\n          ref = this._responseHeaders;\n          results = [];\n          for (name in ref) {\n            value = ref[name];\n            results.push(`${name}: ${value}`);\n          }\n          return results;\n        }).call(this);\n        return lines.join(\"\\r\\n\");\n      }\n\n      // Overrides the Content-Type\n\n      // @return {undefined} undefined\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-overridemimetype()-method\n      overrideMimeType(newMimeType) {\n        if (this.readyState === XMLHttpRequest.LOADING || this.readyState === XMLHttpRequest.DONE) {\n          throw new InvalidStateError(\"overrideMimeType() not allowed in LOADING or DONE\");\n        }\n        this._mimeOverride = newMimeType.toLowerCase();\n        return void 0;\n      }\n\n      // Network configuration not exposed in the XHR API.\n\n      // Although the XMLHttpRequest specification calls itself \"ECMAScript HTTP\",\n      // it assumes that requests are always performed in the context of a browser\n      // application, where some network parameters are set by the browser user and\n      // should not be modified by Web applications. This API provides access to\n      // these network parameters.\n\n      // NOTE: this is not in the XMLHttpRequest API, and will not work in\n      // browsers.  It is a stable node-xhr2 API.\n\n      // @param {Object} options one or more of the options below\n      // @option options {?http.Agent} httpAgent the value for the nodejsHttpAgent\n      //   property (the agent used for HTTP requests)\n      // @option options {?https.Agent} httpsAgent the value for the\n      //   nodejsHttpsAgent property (the agent used for HTTPS requests)\n      // @return {undefined} undefined\n      nodejsSet(options) {\n        var baseUrl, parsedUrl;\n        if ('httpAgent' in options) {\n          this.nodejsHttpAgent = options.httpAgent;\n        }\n        if ('httpsAgent' in options) {\n          this.nodejsHttpsAgent = options.httpsAgent;\n        }\n        if ('baseUrl' in options) {\n          baseUrl = options.baseUrl;\n          if (baseUrl !== null) {\n            parsedUrl = url.parse(baseUrl, false, true);\n            if (!parsedUrl.protocol) {\n              throw new SyntaxError(\"baseUrl must be an absolute URL\");\n            }\n          }\n          this.nodejsBaseUrl = baseUrl;\n        }\n        return void 0;\n      }\n\n      // Default settings for the network configuration not exposed in the XHR API.\n\n      // NOTE: this is not in the XMLHttpRequest API, and will not work in\n      // browsers.  It is a stable node-xhr2 API.\n\n      // @param {Object} options one or more of the options below\n      // @option options {?http.Agent} httpAgent the default value for the\n      //   nodejsHttpAgent property (the agent used for HTTP requests)\n      // @option options {https.Agent} httpsAgent the default value for the\n      //   nodejsHttpsAgent property (the agent used for HTTPS requests)\n      // @return {undefined} undefined\n      // @see XMLHttpRequest.nodejsSet\n      static nodejsSet(options) {\n        // \"this\" will be set to XMLHttpRequest.prototype, so the instance nodejsSet\n        // operates on default property values.\n        XMLHttpRequest.prototype.nodejsSet(options);\n        return void 0;\n      }\n\n      // Sets the readyState property and fires the readystatechange event.\n\n      // @private\n      // @param {Number} newReadyState the new value of readyState\n      // @return {undefined} undefined\n      _setReadyState(newReadyState) {\n        var event;\n        this.readyState = newReadyState;\n        event = new ProgressEvent('readystatechange');\n        this.dispatchEvent(event);\n        return void 0;\n      }\n\n      // XMLHttpRequest#send() implementation for the file: protocol.\n\n      // @private\n      _sendFile() {\n        if (this._url.method !== 'GET') {\n          throw new NetworkError('The file protocol only supports GET');\n        }\n        throw new Error(\"Protocol file: not implemented\");\n      }\n\n      // XMLHttpRequest#send() implementation for the http: and https: protocols.\n\n      // @private\n      // This method sets the instance variables and calls _sendHxxpRequest(), which\n      // is responsible for building a node.js request and firing it off. The code\n      // in _sendHxxpRequest() is separated off so it can be reused when handling\n      // redirects.\n\n      // @see http://www.w3.org/TR/XMLHttpRequest/#infrastructure-for-the-send()-method\n      _sendHttp(data) {\n        if (this._sync) {\n          throw new Error(\"Synchronous XHR processing not implemented\");\n        }\n        if ((data != null) && (this._method === 'GET' || this._method === 'HEAD')) {\n          console.warn(`Discarding entity body for ${this._method} requests`);\n          data = null;\n        } else {\n          // Send Content-Length: 0\n          data || (data = '');\n        }\n        // NOTE: this is called before finalizeHeaders so that the uploader can\n        //       figure out Content-Length and Content-Type.\n        this.upload._setData(data);\n        this._finalizeHeaders();\n        this._sendHxxpRequest();\n        return void 0;\n      }\n\n      // Sets up and fires off a HTTP/HTTPS request using the node.js API.\n\n      // @private\n      // This method contains the bulk of the XMLHttpRequest#send() implementation,\n      // and is also used to issue new HTTP requests when handling HTTP redirects.\n\n      // @see http://www.w3.org/TR/XMLHttpRequest/#infrastructure-for-the-send()-method\n      _sendHxxpRequest() {\n        var agent, hxxp, request;\n        if (this._url.protocol === 'http:') {\n          hxxp = http;\n          agent = this.nodejsHttpAgent;\n        } else {\n          hxxp = https;\n          agent = this.nodejsHttpsAgent;\n        }\n        request = hxxp.request({\n          hostname: this._url.hostname,\n          port: this._url.port,\n          path: this._url.path,\n          auth: this._url.auth,\n          method: this._method,\n          headers: this._headers,\n          agent: agent\n        });\n        this._request = request;\n        if (this.timeout) {\n          request.setTimeout(this.timeout, () => {\n            return this._onHttpTimeout(request);\n          });\n        }\n        request.on('response', (response) => {\n          return this._onHttpResponse(request, response);\n        });\n        request.on('error', (error) => {\n          return this._onHttpRequestError(request, error);\n        });\n        this.upload._startUpload(request);\n        if (this._request === request) { // An http error might have already fired.\n          this._dispatchProgress('loadstart');\n        }\n        return void 0;\n      }\n\n      // Fills in the restricted HTTP headers with default values.\n\n      // This is called right before the HTTP request is sent off.\n\n      // @private\n      // @return {undefined} undefined\n      _finalizeHeaders() {\n        var base;\n        this._headers['Connection'] = 'keep-alive';\n        this._headers['Host'] = this._url.host;\n        if (this._anonymous) {\n          this._headers['Referer'] = 'about:blank';\n        }\n        (base = this._headers)['User-Agent'] || (base['User-Agent'] = this._userAgent);\n        this.upload._finalizeHeaders(this._headers, this._loweredHeaders);\n        return void 0;\n      }\n\n      // Called when the headers of an HTTP response have been received.\n\n      // @private\n      // @param {http.ClientRequest} request the node.js ClientRequest instance that\n      //   produced this response\n      // @param {http.ClientResponse} response the node.js ClientResponse instance\n      //   passed to\n      _onHttpResponse(request, response) {\n        var lengthString;\n        if (this._request !== request) {\n          return;\n        }\n        // Transparent redirection handling.\n        switch (response.statusCode) {\n          case 301:\n          case 302:\n          case 303:\n          case 307:\n          case 308:\n            this._url = this._parseUrl(response.headers['location']);\n            this._method = 'GET';\n            if ('content-type' in this._loweredHeaders) {\n              delete this._headers[this._loweredHeaders['content-type']];\n              delete this._loweredHeaders['content-type'];\n            }\n            // XMLHttpRequestUpload#_finalizeHeaders() sets Content-Type directly.\n            if ('Content-Type' in this._headers) {\n              delete this._headers['Content-Type'];\n            }\n            // Restricted headers can't be set by the user, no need to check\n            // loweredHeaders.\n            delete this._headers['Content-Length'];\n            this.upload._reset();\n            this._finalizeHeaders();\n            this._sendHxxpRequest();\n            return;\n        }\n        this._response = response;\n        this._response.on('data', (data) => {\n          return this._onHttpResponseData(response, data);\n        });\n        this._response.on('end', () => {\n          return this._onHttpResponseEnd(response);\n        });\n        this._response.on('close', () => {\n          return this._onHttpResponseClose(response);\n        });\n        this.responseURL = this._url.href.split('#')[0];\n        this.status = this._response.statusCode;\n        this.statusText = http.STATUS_CODES[this.status];\n        this._parseResponseHeaders(response);\n        if (lengthString = this._responseHeaders['content-length']) {\n          this._totalBytes = parseInt(lengthString);\n          this._lengthComputable = true;\n        } else {\n          this._lengthComputable = false;\n        }\n        return this._setReadyState(XMLHttpRequest.HEADERS_RECEIVED);\n      }\n\n      // Called when some data has been received on a HTTP connection.\n\n      // @private\n      // @param {http.ClientResponse} response the node.js ClientResponse instance\n      //   that fired this event\n      // @param {String, Buffer} data the data that has been received\n      _onHttpResponseData(response, data) {\n        if (this._response !== response) {\n          return;\n        }\n        this._responseParts.push(data);\n        this._loadedBytes += data.length;\n        if (this.readyState !== XMLHttpRequest.LOADING) {\n          this._setReadyState(XMLHttpRequest.LOADING);\n        }\n        return this._dispatchProgress('progress');\n      }\n\n      // Called when the HTTP request finished processing.\n\n      // @private\n      // @param {http.ClientResponse} response the node.js ClientResponse instance\n      //   that fired this event\n      _onHttpResponseEnd(response) {\n        if (this._response !== response) {\n          return;\n        }\n        this._parseResponse();\n        this._request = null;\n        this._response = null;\n        this._setReadyState(XMLHttpRequest.DONE);\n        this._dispatchProgress('load');\n        return this._dispatchProgress('loadend');\n      }\n\n      // Called when the underlying HTTP connection was closed prematurely.\n\n      // If this method is called, it will be called after or instead of\n      // onHttpResponseEnd.\n\n      // @private\n      // @param {http.ClientResponse} response the node.js ClientResponse instance\n      //   that fired this event\n      _onHttpResponseClose(response) {\n        var request;\n        if (this._response !== response) {\n          return;\n        }\n        request = this._request;\n        this._setError();\n        request.abort();\n        this._setReadyState(XMLHttpRequest.DONE);\n        this._dispatchProgress('error');\n        return this._dispatchProgress('loadend');\n      }\n\n      // Called when the timeout set on the HTTP socket expires.\n\n      // @private\n      // @param {http.ClientRequest} request the node.js ClientRequest instance that\n      //   fired this event\n      _onHttpTimeout(request) {\n        if (this._request !== request) {\n          return;\n        }\n        this._setError();\n        request.abort();\n        this._setReadyState(XMLHttpRequest.DONE);\n        this._dispatchProgress('timeout');\n        return this._dispatchProgress('loadend');\n      }\n\n      // Called when something wrong happens on the HTTP socket\n\n      // @private\n      // @param {http.ClientRequest} request the node.js ClientRequest instance that\n      //   fired this event\n      // @param {Error} error emitted exception\n      _onHttpRequestError(request, error) {\n        if (this._request !== request) {\n          return;\n        }\n        this._setError();\n        request.abort();\n        this._setReadyState(XMLHttpRequest.DONE);\n        this._dispatchProgress('error');\n        return this._dispatchProgress('loadend');\n      }\n\n      // Fires an XHR progress event.\n\n      // @private\n      // @param {String} eventType one of the XHR progress event types, such as\n      //   'load' and 'progress'\n      _dispatchProgress(eventType) {\n        var event;\n        event = new ProgressEvent(eventType);\n        event.lengthComputable = this._lengthComputable;\n        event.loaded = this._loadedBytes;\n        event.total = this._totalBytes;\n        this.dispatchEvent(event);\n        return void 0;\n      }\n\n      // Sets up the XHR to reflect the fact that an error has occurred.\n\n      // The possible errors are a network error, a timeout, or an abort.\n\n      // @private\n      _setError() {\n        this._request = null;\n        this._response = null;\n        this._responseHeaders = null;\n        this._responseParts = null;\n        return void 0;\n      }\n\n      // Parses a request URL string.\n\n      // @private\n      // This method is a thin wrapper around url.parse() that normalizes HTTP\n      // user/password credentials. It is used to parse the URL string passed to\n      // XMLHttpRequest#open() and the URLs in the Location headers of HTTP redirect\n      // responses.\n\n      // @param {String} urlString the URL to be parsed\n      // @return {Object} parsed URL\n      _parseUrl(urlString) {\n        var absoluteUrlString, index, password, user, xhrUrl;\n        if (this.nodejsBaseUrl === null) {\n          absoluteUrlString = urlString;\n        } else {\n          absoluteUrlString = url.resolve(this.nodejsBaseUrl, urlString);\n        }\n        xhrUrl = url.parse(absoluteUrlString, false, true);\n        xhrUrl.hash = null;\n        if (xhrUrl.auth && ((typeof user !== \"undefined\" && user !== null) || (typeof password !== \"undefined\" && password !== null))) {\n          index = xhrUrl.auth.indexOf(':');\n          if (index === -1) {\n            if (!user) {\n              user = xhrUrl.auth;\n            }\n          } else {\n            if (!user) {\n              user = xhrUrl.substring(0, index);\n            }\n            if (!password) {\n              password = xhrUrl.substring(index + 1);\n            }\n          }\n        }\n        if (user || password) {\n          xhrUrl.auth = `${user}:${password}`;\n        }\n        return xhrUrl;\n      }\n\n      // Reads the headers from a node.js ClientResponse instance.\n\n      // @private\n      // @param {http.ClientResponse} response the response whose headers will be\n      //   imported into this XMLHttpRequest's state\n      // @return {undefined} undefined\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-getresponseheader()-method\n      // @see http://www.w3.org/TR/XMLHttpRequest/#the-getallresponseheaders()-method\n      _parseResponseHeaders(response) {\n        var loweredName, name, ref, value;\n        this._responseHeaders = {};\n        ref = response.headers;\n        for (name in ref) {\n          value = ref[name];\n          loweredName = name.toLowerCase();\n          if (this._privateHeaders[loweredName]) {\n            continue;\n          }\n          if (this._mimeOverride !== null && loweredName === 'content-type') {\n            value = this._mimeOverride;\n          }\n          this._responseHeaders[loweredName] = value;\n        }\n        if (this._mimeOverride !== null && !('content-type' in this._responseHeaders)) {\n          this._responseHeaders['content-type'] = this._mimeOverride;\n        }\n        return void 0;\n      }\n\n      // Sets the response and responseText properties when an XHR completes.\n\n      // @private\n      // @return {undefined} undefined\n      _parseResponse() {\n        var arrayBuffer, buffer, i, j, jsonError, ref, view;\n        if (Buffer.concat) {\n          buffer = Buffer.concat(this._responseParts);\n        } else {\n          // node 0.6\n          buffer = this._concatBuffers(this._responseParts);\n        }\n        this._responseParts = null;\n        switch (this.responseType) {\n          case 'text':\n            this._parseTextResponse(buffer);\n            break;\n          case 'json':\n            this.responseText = null;\n            try {\n              this.response = JSON.parse(buffer.toString('utf-8'));\n            } catch (error1) {\n              jsonError = error1;\n              this.response = null;\n            }\n            break;\n          case 'buffer':\n            this.responseText = null;\n            this.response = buffer;\n            break;\n          case 'arraybuffer':\n            this.responseText = null;\n            arrayBuffer = new ArrayBuffer(buffer.length);\n            view = new Uint8Array(arrayBuffer);\n            for (i = j = 0, ref = buffer.length; (0 <= ref ? j < ref : j > ref); i = 0 <= ref ? ++j : --j) {\n              view[i] = buffer[i];\n            }\n            this.response = arrayBuffer;\n            break;\n          default:\n            // TODO(pwnall): content-base detection\n            this._parseTextResponse(buffer);\n        }\n        return void 0;\n      }\n\n      // Sets response and responseText for a 'text' response type.\n\n      // @private\n      // @param {Buffer} buffer the node.js Buffer containing the binary response\n      // @return {undefined} undefined\n      _parseTextResponse(buffer) {\n        var e;\n        try {\n          this.responseText = buffer.toString(this._parseResponseEncoding());\n        } catch (error1) {\n          e = error1;\n          // Unknown encoding.\n          this.responseText = buffer.toString('binary');\n        }\n        this.response = this.responseText;\n        return void 0;\n      }\n\n      // Figures out the string encoding of the XHR's response.\n\n      // This is called to determine the encoding when responseText is set.\n\n      // @private\n      // @return {String} a string encoding, e.g. 'utf-8'\n      _parseResponseEncoding() {\n        var contentType, encoding, match;\n        encoding = null;\n        if (contentType = this._responseHeaders['content-type']) {\n          if (match = /\\;\\s*charset\\=(.*)$/.exec(contentType)) {\n            return match[1];\n          }\n        }\n        return 'utf-8';\n      }\n\n      // Buffer.concat implementation for node 0.6.\n\n      // @private\n      // @param {Array<Buffer>} buffers the buffers whose contents will be merged\n      // @return {Buffer} same as Buffer.concat(buffers) in node 0.8 and above\n      _concatBuffers(buffers) {\n        var buffer, j, k, len, len1, length, target;\n        if (buffers.length === 0) {\n          return Buffer.alloc(0);\n        }\n        if (buffers.length === 1) {\n          return buffers[0];\n        }\n        length = 0;\n        for (j = 0, len = buffers.length; j < len; j++) {\n          buffer = buffers[j];\n          length += buffer.length;\n        }\n        target = Buffer.alloc(length);\n        length = 0;\n        for (k = 0, len1 = buffers.length; k < len1; k++) {\n          buffer = buffers[k];\n          buffer.copy(target, length);\n          length += buffer.length;\n        }\n        return target;\n      }\n\n    };\n\n    // @property {function(ProgressEvent)} DOM level 0-style handler for the\n    //   'readystatechange' event\n    XMLHttpRequest.prototype.onreadystatechange = null;\n\n    // @property {Number} the current state of the XHR object\n    // @see http://www.w3.org/TR/XMLHttpRequest/#states\n    XMLHttpRequest.prototype.readyState = null;\n\n    // @property {String, ArrayBuffer, Buffer, Object} processed XHR response\n    // @see http://www.w3.org/TR/XMLHttpRequest/#the-response-attribute\n    XMLHttpRequest.prototype.response = null;\n\n    // @property {String} response string, if responseType is '' or 'text'\n    // @see http://www.w3.org/TR/XMLHttpRequest/#the-responsetext-attribute\n    XMLHttpRequest.prototype.responseText = null;\n\n    // @property {String} sets the parsing method for the XHR response\n    // @see http://www.w3.org/TR/XMLHttpRequest/#the-responsetype-attribute\n    XMLHttpRequest.prototype.responseType = null;\n\n    // @property {Number} the HTTP\n    // @see http://www.w3.org/TR/XMLHttpRequest/#the-status-attribute\n    XMLHttpRequest.prototype.status = null;\n\n    // @property {Number} milliseconds to wait for the request to complete\n    // @see http://www.w3.org/TR/XMLHttpRequest/#the-timeout-attribute\n    XMLHttpRequest.prototype.timeout = null;\n\n    // @property {XMLHttpRequestUpload} the associated upload information\n    // @see http://www.w3.org/TR/XMLHttpRequest/#the-upload-attribute\n    XMLHttpRequest.prototype.upload = null;\n\n    // readyState value before XMLHttpRequest#open() is called\n    XMLHttpRequest.prototype.UNSENT = 0;\n\n    // readyState value before XMLHttpRequest#open() is called\n    XMLHttpRequest.UNSENT = 0;\n\n    // readyState value after XMLHttpRequest#open() is called, and before\n    //   XMLHttpRequest#send() is called; XMLHttpRequest#setRequestHeader() can be\n    //   called in this state\n    XMLHttpRequest.prototype.OPENED = 1;\n\n    // readyState value after XMLHttpRequest#open() is called, and before\n    //   XMLHttpRequest#send() is called; XMLHttpRequest#setRequestHeader() can be\n    //   called in this state\n    XMLHttpRequest.OPENED = 1;\n\n    // readyState value after redirects have been followed and the HTTP headers of\n    //   the final response have been received\n    XMLHttpRequest.prototype.HEADERS_RECEIVED = 2;\n\n    // readyState value after redirects have been followed and the HTTP headers of\n    //   the final response have been received\n    XMLHttpRequest.HEADERS_RECEIVED = 2;\n\n    // readyState value when the response entity body is being received\n    XMLHttpRequest.prototype.LOADING = 3;\n\n    // readyState value when the response entity body is being received\n    XMLHttpRequest.LOADING = 3;\n\n    // readyState value after the request has been completely processed\n    XMLHttpRequest.prototype.DONE = 4;\n\n    // readyState value after the request has been completely processed\n    XMLHttpRequest.DONE = 4;\n\n    // @property {http.Agent} the agent option passed to HTTP requests\n\n    // NOTE: this is not in the XMLHttpRequest API, and will not work in browsers.\n    // It is a stable node-xhr2 API that is useful for testing & going through\n    // web-proxies.\n    XMLHttpRequest.prototype.nodejsHttpAgent = http.globalAgent;\n\n    // @property {https.Agent} the agent option passed to HTTPS requests\n\n    // NOTE: this is not in the XMLHttpRequest API, and will not work in browsers.\n    // It is a stable node-xhr2 API that is useful for testing & going through\n    // web-proxies.\n    XMLHttpRequest.prototype.nodejsHttpsAgent = https.globalAgent;\n\n    // @property {String} the base URL that relative URLs get resolved to\n\n    // NOTE: this is not in the XMLHttpRequest API, and will not work in browsers.\n    // Its browser equivalent is the base URL of the document associated with the\n    // Window object. It is a stable node-xhr2 API provided for libraries such as\n    // Angular Universal.\n    XMLHttpRequest.prototype.nodejsBaseUrl = null;\n\n    // HTTP methods that are disallowed in the XHR spec.\n\n    // @private\n    // @see Step 6 in http://www.w3.org/TR/XMLHttpRequest/#the-open()-method\n    XMLHttpRequest.prototype._restrictedMethods = {\n      CONNECT: true,\n      TRACE: true,\n      TRACK: true\n    };\n\n    // HTTP request headers that are disallowed in the XHR spec.\n\n    // @private\n    // @see Step 5 in\n    //   http://www.w3.org/TR/XMLHttpRequest/#the-setrequestheader()-method\n    XMLHttpRequest.prototype._restrictedHeaders = {\n      'accept-charset': true,\n      'accept-encoding': true,\n      'access-control-request-headers': true,\n      'access-control-request-method': true,\n      connection: true,\n      'content-length': true,\n      cookie: true,\n      cookie2: true,\n      date: true,\n      dnt: true,\n      expect: true,\n      host: true,\n      'keep-alive': true,\n      origin: true,\n      referer: true,\n      te: true,\n      trailer: true,\n      'transfer-encoding': true,\n      upgrade: true,\n      via: true\n    };\n\n    // HTTP response headers that should not be exposed according to the XHR spec.\n\n    // @private\n    // @see Step 3 in\n    //     http://www.w3.org/TR/XMLHttpRequest/#the-getresponseheader()-method\n    XMLHttpRequest.prototype._privateHeaders = {\n      'set-cookie': true,\n      'set-cookie2': true\n    };\n\n    // The default value of the User-Agent header.\n    XMLHttpRequest.prototype._userAgent = `Mozilla/5.0 (${os.type()} ${os.arch()}) ` + `node.js/${process.versions.node} v8/${process.versions.v8}`;\n\n    return XMLHttpRequest;\n\n  }).call(this);\n\n  // XMLHttpRequest is the result of require('node-xhr2').\n  module.exports = XMLHttpRequest;\n\n  // Make node-xhr2 work as a drop-in replacement for libraries that promote the\n  // following usage pattern:\n  //     var XMLHttpRequest = require('xhr-library-name').XMLHttpRequest\n  XMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n\n  // This file defines the custom errors used in the XMLHttpRequest specification.\n\n    // Thrown if the XHR security policy is violated.\n  SecurityError = class SecurityError extends Error {\n    // @private\n    constructor() {\n      super();\n    }\n\n  };\n\n  // Thrown if the XHR security policy is violated.\n  XMLHttpRequest.SecurityError = SecurityError;\n\n  // Usually thrown if the XHR is in the wrong readyState for an operation.\n  InvalidStateError = class InvalidStateError extends Error {\n    // @private\n    constructor() {\n      super();\n    }\n\n  };\n\n  // Usually thrown if the XHR is in the wrong readyState for an operation.\n  InvalidStateError = class InvalidStateError extends Error {};\n\n  XMLHttpRequest.InvalidStateError = InvalidStateError;\n\n  // Thrown if there is a problem with the URL passed to the XHR.\n  NetworkError = class NetworkError extends Error {\n    // @private\n    constructor() {\n      super();\n    }\n\n  };\n\n  // Thrown if parsing URLs errors out.\n  XMLHttpRequest.SyntaxError = SyntaxError;\n\n  SyntaxError = class SyntaxError extends Error {\n    // @private:\n    constructor() {\n      super();\n    }\n\n  };\n\n  ProgressEvent = (function() {\n    // http://xhr.spec.whatwg.org/#interface-progressevent\n    class ProgressEvent {\n      // Creates a new event.\n\n      // @param {String} type the event type, e.g. 'readystatechange'; must be\n      //   lowercased\n      constructor(type) {\n        this.type = type;\n        this.target = null;\n        this.currentTarget = null;\n        this.lengthComputable = false;\n        this.loaded = 0;\n        this.total = 0;\n      }\n\n    };\n\n    // Getting the time from the OS is expensive, skip on that for now.\n    // @timeStamp = Date.now()\n\n    // @property {Boolean} for compatibility with DOM events\n    ProgressEvent.prototype.bubbles = false;\n\n    // @property {Boolean} for fompatibility with DOM events\n    ProgressEvent.prototype.cancelable = false;\n\n    // @property {XMLHttpRequest} the request that caused this event\n    ProgressEvent.prototype.target = null;\n\n    // @property {Number} number of bytes that have already been downloaded or\n    //   uploaded\n    ProgressEvent.prototype.loaded = null;\n\n    // @property {Boolean} true if the Content-Length response header is available\n    //   and the value of the event's total property is meaningful\n    ProgressEvent.prototype.lengthComputable = null;\n\n    // @property {Number} number of bytes that will be downloaded or uploaded by\n    //   the request that fired the event\n    ProgressEvent.prototype.total = null;\n\n    return ProgressEvent;\n\n  }).call(this);\n\n  // The XHR spec exports the ProgressEvent constructor.\n  XMLHttpRequest.ProgressEvent = ProgressEvent;\n\n  // @see http://xhr.spec.whatwg.org/#interface-xmlhttprequest\n  XMLHttpRequestUpload = class XMLHttpRequestUpload extends XMLHttpRequestEventTarget {\n    // @private\n    // @param {XMLHttpRequest} the XMLHttpRequest that this upload object is\n    //   associated with\n    constructor(request) {\n      super();\n      this._request = request;\n      this._reset();\n    }\n\n    // Sets up this Upload to handle a new request.\n\n    // @private\n    // @return {undefined} undefined\n    _reset() {\n      this._contentType = null;\n      this._body = null;\n      return void 0;\n    }\n\n    // Implements the upload-related part of the send() XHR specification.\n\n    // @private\n    // @param {?String, ?Buffer, ?ArrayBufferView} data the argument passed to\n    //   XMLHttpRequest#send()\n    // @return {undefined} undefined\n    // @see step 4 of http://www.w3.org/TR/XMLHttpRequest/#the-send()-method\n    _setData(data) {\n      var body, i, j, k, offset, ref, ref1, view;\n      if (typeof data === 'undefined' || data === null) {\n        return;\n      }\n      if (typeof data === 'string') {\n        // DOMString\n        if (data.length !== 0) {\n          this._contentType = 'text/plain;charset=UTF-8';\n        }\n        this._body = Buffer.from(data, 'utf8');\n      } else if (Buffer.isBuffer(data)) {\n        // node.js Buffer\n        this._body = data;\n      } else if (data instanceof ArrayBuffer) {\n        // ArrayBuffer arguments were supported in an old revision of the spec.\n        body = Buffer.alloc(data.byteLength);\n        view = new Uint8Array(data);\n        for (i = j = 0, ref = data.byteLength; (0 <= ref ? j < ref : j > ref); i = 0 <= ref ? ++j : --j) {\n          body[i] = view[i];\n        }\n        this._body = body;\n      } else if (data.buffer && data.buffer instanceof ArrayBuffer) {\n        // ArrayBufferView\n        body = Buffer.alloc(data.byteLength);\n        offset = data.byteOffset;\n        view = new Uint8Array(data.buffer);\n        for (i = k = 0, ref1 = data.byteLength; (0 <= ref1 ? k < ref1 : k > ref1); i = 0 <= ref1 ? ++k : --k) {\n          body[i] = view[i + offset];\n        }\n        this._body = body;\n      } else {\n        // NOTE: diverging from the XHR specification of coercing everything else\n        //       to Strings via toString() because that behavior masks bugs and is\n        //       rarely useful\n        throw new Error(`Unsupported send() data ${data}`);\n      }\n      return void 0;\n    }\n\n    // Updates the HTTP headers right before the request is sent.\n\n    // This is used to set data-dependent headers such as Content-Length and\n    // Content-Type.\n\n    // @private\n    // @param {Object<String, String>} headers the HTTP headers to be sent\n    // @param {Object<String, String>} loweredHeaders maps lowercased HTTP header\n    //   names (e.g., 'content-type') to the actual names used in the headers\n    //   parameter (e.g., 'Content-Type')\n    // @return {undefined} undefined\n    _finalizeHeaders(headers, loweredHeaders) {\n      if (this._contentType) {\n        if (!('content-type' in loweredHeaders)) {\n          headers['Content-Type'] = this._contentType;\n        }\n      }\n      if (this._body) {\n        // Restricted headers can't be set by the user, no need to check\n        // loweredHeaders.\n        headers['Content-Length'] = this._body.length.toString();\n      }\n      return void 0;\n    }\n\n    // Starts sending the HTTP request data.\n\n    // @private\n    // @param {http.ClientRequest} request the HTTP request\n    // @return {undefined} undefined\n    _startUpload(request) {\n      if (this._body) {\n        request.write(this._body);\n      }\n      request.end();\n      return void 0;\n    }\n\n  };\n\n  // Export the XMLHttpRequestUpload constructor.\n  XMLHttpRequest.XMLHttpRequestUpload = XMLHttpRequestUpload;\n\n}).call(this);\n"], "mappings": ";;;;;;;AAAA;AAAA;AACA,KAAC,WAAW;AAQV,UAAI,mBAAmB,cAAc,eAAe,eAAe,aAAa,gBAAgB,2BAA2B,sBAAsB,MAAM,OAAO,IAAI;AAElK,mCAA6B,WAAW;AAAA,QACtC,MAAMA,2BAA0B;AAAA;AAAA;AAAA,UAG9B,cAAc;AACZ,iBAAK,cAAc;AACnB,iBAAK,aAAa;AAClB,iBAAK,UAAU;AACf,iBAAK,UAAU;AACf,iBAAK,SAAS;AACd,iBAAK,YAAY;AACjB,iBAAK,YAAY;AACjB,iBAAK,aAAa,CAAC;AAAA,UACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUA,iBAAiB,WAAW,UAAU;AACpC,gBAAI;AACJ,wBAAY,UAAU,YAAY;AAClC,aAAC,OAAO,KAAK,YAAY,SAAS,MAAM,KAAK,SAAS,IAAI,CAAC;AAC3D,iBAAK,WAAW,SAAS,EAAE,KAAK,QAAQ;AACxC,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,oBAAoB,WAAW,UAAU;AACvC,gBAAI;AACJ,wBAAY,UAAU,YAAY;AAClC,gBAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,sBAAQ,KAAK,WAAW,SAAS,EAAE,QAAQ,QAAQ;AACnD,kBAAI,UAAU,IAAI;AAChB,qBAAK,WAAW,SAAS,EAAE,OAAO,OAAO,CAAC;AAAA,cAC5C;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAMA,cAAc,OAAO;AACnB,gBAAI,WAAW,GAAG,KAAK,UAAU;AACjC,kBAAM,gBAAgB,MAAM,SAAS;AACrC,wBAAY,MAAM;AAClB,gBAAI,YAAY,KAAK,WAAW,SAAS,GAAG;AAC1C,mBAAK,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK;AAChD,2BAAW,UAAU,CAAC;AACtB,yBAAS,KAAK,MAAM,KAAK;AAAA,cAC3B;AAAA,YACF;AACA,gBAAI,WAAW,KAAK,KAAK,SAAS,EAAE,GAAG;AACrC,uBAAS,KAAK,MAAM,KAAK;AAAA,YAC3B;AACA,mBAAO;AAAA,UACT;AAAA,QAEF;AAAC;AAID,QAAAA,2BAA0B,UAAU,cAAc;AAIlD,QAAAA,2BAA0B,UAAU,aAAa;AAIjD,QAAAA,2BAA0B,UAAU,UAAU;AAI9C,QAAAA,2BAA0B,UAAU,UAAU;AAI9C,QAAAA,2BAA0B,UAAU,SAAS;AAI7C,QAAAA,2BAA0B,UAAU,YAAY;AAIhD,QAAAA,2BAA0B,UAAU,YAAY;AAEhD,eAAOA;AAAA,MAET,GAAG,KAAK,IAAI;AAMZ,aAAO,UAAQ,MAAM;AAErB,cAAQ,UAAQ,OAAO;AAEvB,WAAK,UAAQ,IAAI;AAEjB,YAAM,UAAQ,KAAK;AAEnB,wBAAkB,WAAW;AAAA,QAI3B,MAAMC,wBAAuB,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQrD,YAAY,SAAS;AACnB,kBAAM;AACN,iBAAK,qBAAqB;AAC1B,iBAAK,aAAa,WAAW,QAAQ;AACrC,iBAAK,aAAaA,gBAAe;AACjC,iBAAK,WAAW;AAChB,iBAAK,eAAe;AACpB,iBAAK,eAAe;AACpB,iBAAK,cAAc;AACnB,iBAAK,SAAS;AACd,iBAAK,aAAa;AAClB,iBAAK,UAAU;AACf,iBAAK,SAAS,IAAI,qBAAqB,IAAI;AAC3C,iBAAK,UAAU;AACf,iBAAK,OAAO;AACZ,iBAAK,QAAQ;AACb,iBAAK,WAAW;AAChB,iBAAK,kBAAkB;AACvB,iBAAK,gBAAgB;AACrB,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,iBAAiB;AACtB,iBAAK,mBAAmB;AACxB,iBAAK,YAAY;AACjB,iBAAK,SAAS;AACd,iBAAK,eAAe;AACpB,iBAAK,cAAc;AACnB,iBAAK,oBAAoB;AAAA,UAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAkBA,KAAK,QAAQC,MAAK,OAAO,MAAM,UAAU;AACvC,gBAAI;AACJ,qBAAS,OAAO,YAAY;AAC5B,gBAAI,UAAU,KAAK,oBAAoB;AACrC,oBAAM,IAAI,cAAc,eAAe,MAAM,wBAAwB;AAAA,YACvE;AACA,qBAAS,KAAK,UAAUA,IAAG;AAC3B,gBAAI,UAAU,QAAQ;AACpB,sBAAQ;AAAA,YACV;AACA,oBAAQ,KAAK,YAAY;AAAA,cACvB,KAAKD,gBAAe;AAAA,cACpB,KAAKA,gBAAe;AAAA,cACpB,KAAKA,gBAAe;AAElB;AACA;AAAA,cACF,KAAKA,gBAAe;AAAA,cACpB,KAAKA,gBAAe;AAElB;AAAA,YACJ;AACA,iBAAK,UAAU;AACf,iBAAK,OAAO;AACZ,iBAAK,QAAQ,CAAC;AACd,iBAAK,WAAW,CAAC;AACjB,iBAAK,kBAAkB,CAAC;AACxB,iBAAK,gBAAgB;AACrB,iBAAK,eAAeA,gBAAe,MAAM;AACzC,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,SAAS;AACd,iBAAK,aAAa;AAClB,iBAAK,iBAAiB,CAAC;AACvB,iBAAK,mBAAmB;AACxB,iBAAK,eAAe;AACpB,iBAAK,cAAc;AACnB,iBAAK,oBAAoB;AACzB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA,iBAAiB,MAAM,OAAO;AAC5B,gBAAI;AACJ,gBAAI,KAAK,eAAeA,gBAAe,QAAQ;AAC7C,oBAAM,IAAI,kBAAkB,+BAA+B;AAAA,YAC7D;AACA,0BAAc,KAAK,YAAY;AAC/B,gBAAI,KAAK,mBAAmB,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,UAAU,KAAK,WAAW,GAAG;AACrG,sBAAQ,KAAK,iCAAkC,IAAI,GAAI;AACvD,qBAAO;AAAA,YACT;AACA,oBAAQ,MAAM,SAAS;AACvB,gBAAI,eAAe,KAAK,iBAAiB;AAEvC,qBAAO,KAAK,gBAAgB,WAAW;AACvC,mBAAK,SAAS,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,OAAO;AAAA,YACrD,OAAO;AAEL,mBAAK,gBAAgB,WAAW,IAAI;AACpC,mBAAK,SAAS,IAAI,IAAI;AAAA,YACxB;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,KAAK,MAAM;AACT,gBAAI,KAAK,eAAeA,gBAAe,QAAQ;AAC7C,oBAAM,IAAI,kBAAkB,+BAA+B;AAAA,YAC7D;AACA,gBAAI,KAAK,UAAU;AACjB,oBAAM,IAAI,kBAAkB,uBAAuB;AAAA,YACrD;AACA,oBAAQ,KAAK,KAAK,UAAU;AAAA,cAC1B,KAAK;AACH,qBAAK,UAAU,IAAI;AACnB;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AACH,qBAAK,UAAU,IAAI;AACnB;AAAA,cACF;AACE,sBAAM,IAAI,aAAa,wBAAwB,KAAK,KAAK,QAAQ,EAAE;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAMA,QAAQ;AACN,gBAAI,CAAC,KAAK,UAAU;AAClB;AAAA,YACF;AACA,iBAAK,SAAS,MAAM;AACpB,iBAAK,UAAU;AACf,iBAAK,kBAAkB,OAAO;AAC9B,iBAAK,kBAAkB,SAAS;AAChC,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,kBAAkB,MAAM;AACtB,gBAAI;AACJ,gBAAI,CAAC,KAAK,kBAAkB;AAC1B,qBAAO;AAAA,YACT;AACA,0BAAc,KAAK,YAAY;AAC/B,gBAAI,eAAe,KAAK,kBAAkB;AACxC,qBAAO,KAAK,iBAAiB,WAAW;AAAA,YAC1C,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,wBAAwB;AACtB,gBAAI,OAAO,MAAM;AACjB,gBAAI,CAAC,KAAK,kBAAkB;AAC1B,qBAAO;AAAA,YACT;AACA,qBAAS,WAAW;AAClB,kBAAI,KAAK;AACT,oBAAM,KAAK;AACX,wBAAU,CAAC;AACX,mBAAK,QAAQ,KAAK;AAChB,wBAAQ,IAAI,IAAI;AAChB,wBAAQ,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE;AAAA,cAClC;AACA,qBAAO;AAAA,YACT,GAAG,KAAK,IAAI;AACZ,mBAAO,MAAM,KAAK,MAAM;AAAA,UAC1B;AAAA;AAAA;AAAA;AAAA,UAMA,iBAAiB,aAAa;AAC5B,gBAAI,KAAK,eAAeA,gBAAe,WAAW,KAAK,eAAeA,gBAAe,MAAM;AACzF,oBAAM,IAAI,kBAAkB,mDAAmD;AAAA,YACjF;AACA,iBAAK,gBAAgB,YAAY,YAAY;AAC7C,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAmBA,UAAU,SAAS;AACjB,gBAAI,SAAS;AACb,gBAAI,eAAe,SAAS;AAC1B,mBAAK,kBAAkB,QAAQ;AAAA,YACjC;AACA,gBAAI,gBAAgB,SAAS;AAC3B,mBAAK,mBAAmB,QAAQ;AAAA,YAClC;AACA,gBAAI,aAAa,SAAS;AACxB,wBAAU,QAAQ;AAClB,kBAAI,YAAY,MAAM;AACpB,4BAAY,IAAI,MAAM,SAAS,OAAO,IAAI;AAC1C,oBAAI,CAAC,UAAU,UAAU;AACvB,wBAAM,IAAI,YAAY,iCAAiC;AAAA,gBACzD;AAAA,cACF;AACA,mBAAK,gBAAgB;AAAA,YACvB;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcA,OAAO,UAAU,SAAS;AAGxB,YAAAA,gBAAe,UAAU,UAAU,OAAO;AAC1C,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,eAAe,eAAe;AAC5B,gBAAI;AACJ,iBAAK,aAAa;AAClB,oBAAQ,IAAI,cAAc,kBAAkB;AAC5C,iBAAK,cAAc,KAAK;AACxB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA,UAKA,YAAY;AACV,gBAAI,KAAK,KAAK,WAAW,OAAO;AAC9B,oBAAM,IAAI,aAAa,qCAAqC;AAAA,YAC9D;AACA,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA,UAAU,MAAM;AACd,gBAAI,KAAK,OAAO;AACd,oBAAM,IAAI,MAAM,4CAA4C;AAAA,YAC9D;AACA,gBAAK,QAAQ,SAAU,KAAK,YAAY,SAAS,KAAK,YAAY,SAAS;AACzE,sBAAQ,KAAK,8BAA8B,KAAK,OAAO,WAAW;AAClE,qBAAO;AAAA,YACT,OAAO;AAEL,uBAAS,OAAO;AAAA,YAClB;AAGA,iBAAK,OAAO,SAAS,IAAI;AACzB,iBAAK,iBAAiB;AACtB,iBAAK,iBAAiB;AACtB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,mBAAmB;AACjB,gBAAI,OAAO,MAAM;AACjB,gBAAI,KAAK,KAAK,aAAa,SAAS;AAClC,qBAAO;AACP,sBAAQ,KAAK;AAAA,YACf,OAAO;AACL,qBAAO;AACP,sBAAQ,KAAK;AAAA,YACf;AACA,sBAAU,KAAK,QAAQ;AAAA,cACrB,UAAU,KAAK,KAAK;AAAA,cACpB,MAAM,KAAK,KAAK;AAAA,cAChB,MAAM,KAAK,KAAK;AAAA,cAChB,MAAM,KAAK,KAAK;AAAA,cAChB,QAAQ,KAAK;AAAA,cACb,SAAS,KAAK;AAAA,cACd;AAAA,YACF,CAAC;AACD,iBAAK,WAAW;AAChB,gBAAI,KAAK,SAAS;AAChB,sBAAQ,WAAW,KAAK,SAAS,MAAM;AACrC,uBAAO,KAAK,eAAe,OAAO;AAAA,cACpC,CAAC;AAAA,YACH;AACA,oBAAQ,GAAG,YAAY,CAAC,aAAa;AACnC,qBAAO,KAAK,gBAAgB,SAAS,QAAQ;AAAA,YAC/C,CAAC;AACD,oBAAQ,GAAG,SAAS,CAAC,UAAU;AAC7B,qBAAO,KAAK,oBAAoB,SAAS,KAAK;AAAA,YAChD,CAAC;AACD,iBAAK,OAAO,aAAa,OAAO;AAChC,gBAAI,KAAK,aAAa,SAAS;AAC7B,mBAAK,kBAAkB,WAAW;AAAA,YACpC;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,mBAAmB;AACjB,gBAAI;AACJ,iBAAK,SAAS,YAAY,IAAI;AAC9B,iBAAK,SAAS,MAAM,IAAI,KAAK,KAAK;AAClC,gBAAI,KAAK,YAAY;AACnB,mBAAK,SAAS,SAAS,IAAI;AAAA,YAC7B;AACA,aAAC,OAAO,KAAK,UAAU,YAAY,MAAM,KAAK,YAAY,IAAI,KAAK;AACnE,iBAAK,OAAO,iBAAiB,KAAK,UAAU,KAAK,eAAe;AAChE,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,gBAAgB,SAAS,UAAU;AACjC,gBAAI;AACJ,gBAAI,KAAK,aAAa,SAAS;AAC7B;AAAA,YACF;AAEA,oBAAQ,SAAS,YAAY;AAAA,cAC3B,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AACH,qBAAK,OAAO,KAAK,UAAU,SAAS,QAAQ,UAAU,CAAC;AACvD,qBAAK,UAAU;AACf,oBAAI,kBAAkB,KAAK,iBAAiB;AAC1C,yBAAO,KAAK,SAAS,KAAK,gBAAgB,cAAc,CAAC;AACzD,yBAAO,KAAK,gBAAgB,cAAc;AAAA,gBAC5C;AAEA,oBAAI,kBAAkB,KAAK,UAAU;AACnC,yBAAO,KAAK,SAAS,cAAc;AAAA,gBACrC;AAGA,uBAAO,KAAK,SAAS,gBAAgB;AACrC,qBAAK,OAAO,OAAO;AACnB,qBAAK,iBAAiB;AACtB,qBAAK,iBAAiB;AACtB;AAAA,YACJ;AACA,iBAAK,YAAY;AACjB,iBAAK,UAAU,GAAG,QAAQ,CAAC,SAAS;AAClC,qBAAO,KAAK,oBAAoB,UAAU,IAAI;AAAA,YAChD,CAAC;AACD,iBAAK,UAAU,GAAG,OAAO,MAAM;AAC7B,qBAAO,KAAK,mBAAmB,QAAQ;AAAA,YACzC,CAAC;AACD,iBAAK,UAAU,GAAG,SAAS,MAAM;AAC/B,qBAAO,KAAK,qBAAqB,QAAQ;AAAA,YAC3C,CAAC;AACD,iBAAK,cAAc,KAAK,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC;AAC9C,iBAAK,SAAS,KAAK,UAAU;AAC7B,iBAAK,aAAa,KAAK,aAAa,KAAK,MAAM;AAC/C,iBAAK,sBAAsB,QAAQ;AACnC,gBAAI,eAAe,KAAK,iBAAiB,gBAAgB,GAAG;AAC1D,mBAAK,cAAc,SAAS,YAAY;AACxC,mBAAK,oBAAoB;AAAA,YAC3B,OAAO;AACL,mBAAK,oBAAoB;AAAA,YAC3B;AACA,mBAAO,KAAK,eAAeA,gBAAe,gBAAgB;AAAA,UAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,oBAAoB,UAAU,MAAM;AAClC,gBAAI,KAAK,cAAc,UAAU;AAC/B;AAAA,YACF;AACA,iBAAK,eAAe,KAAK,IAAI;AAC7B,iBAAK,gBAAgB,KAAK;AAC1B,gBAAI,KAAK,eAAeA,gBAAe,SAAS;AAC9C,mBAAK,eAAeA,gBAAe,OAAO;AAAA,YAC5C;AACA,mBAAO,KAAK,kBAAkB,UAAU;AAAA,UAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,mBAAmB,UAAU;AAC3B,gBAAI,KAAK,cAAc,UAAU;AAC/B;AAAA,YACF;AACA,iBAAK,eAAe;AACpB,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,eAAeA,gBAAe,IAAI;AACvC,iBAAK,kBAAkB,MAAM;AAC7B,mBAAO,KAAK,kBAAkB,SAAS;AAAA,UACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUA,qBAAqB,UAAU;AAC7B,gBAAI;AACJ,gBAAI,KAAK,cAAc,UAAU;AAC/B;AAAA,YACF;AACA,sBAAU,KAAK;AACf,iBAAK,UAAU;AACf,oBAAQ,MAAM;AACd,iBAAK,eAAeA,gBAAe,IAAI;AACvC,iBAAK,kBAAkB,OAAO;AAC9B,mBAAO,KAAK,kBAAkB,SAAS;AAAA,UACzC;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,eAAe,SAAS;AACtB,gBAAI,KAAK,aAAa,SAAS;AAC7B;AAAA,YACF;AACA,iBAAK,UAAU;AACf,oBAAQ,MAAM;AACd,iBAAK,eAAeA,gBAAe,IAAI;AACvC,iBAAK,kBAAkB,SAAS;AAChC,mBAAO,KAAK,kBAAkB,SAAS;AAAA,UACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,oBAAoB,SAAS,OAAO;AAClC,gBAAI,KAAK,aAAa,SAAS;AAC7B;AAAA,YACF;AACA,iBAAK,UAAU;AACf,oBAAQ,MAAM;AACd,iBAAK,eAAeA,gBAAe,IAAI;AACvC,iBAAK,kBAAkB,OAAO;AAC9B,mBAAO,KAAK,kBAAkB,SAAS;AAAA,UACzC;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,kBAAkB,WAAW;AAC3B,gBAAI;AACJ,oBAAQ,IAAI,cAAc,SAAS;AACnC,kBAAM,mBAAmB,KAAK;AAC9B,kBAAM,SAAS,KAAK;AACpB,kBAAM,QAAQ,KAAK;AACnB,iBAAK,cAAc,KAAK;AACxB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAOA,YAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,mBAAmB;AACxB,iBAAK,iBAAiB;AACtB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAYA,UAAU,WAAW;AACnB,gBAAI,mBAAmB,OAAO,UAAU,MAAM;AAC9C,gBAAI,KAAK,kBAAkB,MAAM;AAC/B,kCAAoB;AAAA,YACtB,OAAO;AACL,kCAAoB,IAAI,QAAQ,KAAK,eAAe,SAAS;AAAA,YAC/D;AACA,qBAAS,IAAI,MAAM,mBAAmB,OAAO,IAAI;AACjD,mBAAO,OAAO;AACd,gBAAI,OAAO,SAAU,OAAO,SAAS,eAAe,SAAS,QAAU,OAAO,aAAa,eAAe,aAAa,OAAQ;AAC7H,sBAAQ,OAAO,KAAK,QAAQ,GAAG;AAC/B,kBAAI,UAAU,IAAI;AAChB,oBAAI,CAAC,MAAM;AACT,yBAAO,OAAO;AAAA,gBAChB;AAAA,cACF,OAAO;AACL,oBAAI,CAAC,MAAM;AACT,yBAAO,OAAO,UAAU,GAAG,KAAK;AAAA,gBAClC;AACA,oBAAI,CAAC,UAAU;AACb,6BAAW,OAAO,UAAU,QAAQ,CAAC;AAAA,gBACvC;AAAA,cACF;AAAA,YACF;AACA,gBAAI,QAAQ,UAAU;AACpB,qBAAO,OAAO,GAAG,IAAI,IAAI,QAAQ;AAAA,YACnC;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUA,sBAAsB,UAAU;AAC9B,gBAAI,aAAa,MAAM,KAAK;AAC5B,iBAAK,mBAAmB,CAAC;AACzB,kBAAM,SAAS;AACf,iBAAK,QAAQ,KAAK;AAChB,sBAAQ,IAAI,IAAI;AAChB,4BAAc,KAAK,YAAY;AAC/B,kBAAI,KAAK,gBAAgB,WAAW,GAAG;AACrC;AAAA,cACF;AACA,kBAAI,KAAK,kBAAkB,QAAQ,gBAAgB,gBAAgB;AACjE,wBAAQ,KAAK;AAAA,cACf;AACA,mBAAK,iBAAiB,WAAW,IAAI;AAAA,YACvC;AACA,gBAAI,KAAK,kBAAkB,QAAQ,EAAE,kBAAkB,KAAK,mBAAmB;AAC7E,mBAAK,iBAAiB,cAAc,IAAI,KAAK;AAAA,YAC/C;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAMA,iBAAiB;AACf,gBAAI,aAAa,QAAQ,GAAG,GAAG,WAAW,KAAK;AAC/C,gBAAI,OAAO,QAAQ;AACjB,uBAAS,OAAO,OAAO,KAAK,cAAc;AAAA,YAC5C,OAAO;AAEL,uBAAS,KAAK,eAAe,KAAK,cAAc;AAAA,YAClD;AACA,iBAAK,iBAAiB;AACtB,oBAAQ,KAAK,cAAc;AAAA,cACzB,KAAK;AACH,qBAAK,mBAAmB,MAAM;AAC9B;AAAA,cACF,KAAK;AACH,qBAAK,eAAe;AACpB,oBAAI;AACF,uBAAK,WAAW,KAAK,MAAM,OAAO,SAAS,OAAO,CAAC;AAAA,gBACrD,SAAS,QAAQ;AACf,8BAAY;AACZ,uBAAK,WAAW;AAAA,gBAClB;AACA;AAAA,cACF,KAAK;AACH,qBAAK,eAAe;AACpB,qBAAK,WAAW;AAChB;AAAA,cACF,KAAK;AACH,qBAAK,eAAe;AACpB,8BAAc,IAAI,YAAY,OAAO,MAAM;AAC3C,uBAAO,IAAI,WAAW,WAAW;AACjC,qBAAK,IAAI,IAAI,GAAG,MAAM,OAAO,QAAS,KAAK,MAAM,IAAI,MAAM,IAAI,KAAM,IAAI,KAAK,MAAM,EAAE,IAAI,EAAE,GAAG;AAC7F,uBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,gBACpB;AACA,qBAAK,WAAW;AAChB;AAAA,cACF;AAEE,qBAAK,mBAAmB,MAAM;AAAA,YAClC;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,mBAAmB,QAAQ;AACzB,gBAAI;AACJ,gBAAI;AACF,mBAAK,eAAe,OAAO,SAAS,KAAK,uBAAuB,CAAC;AAAA,YACnE,SAAS,QAAQ;AACf,kBAAI;AAEJ,mBAAK,eAAe,OAAO,SAAS,QAAQ;AAAA,YAC9C;AACA,iBAAK,WAAW,KAAK;AACrB,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,yBAAyB;AACvB,gBAAI,aAAa,UAAU;AAC3B,uBAAW;AACX,gBAAI,cAAc,KAAK,iBAAiB,cAAc,GAAG;AACvD,kBAAI,QAAQ,sBAAsB,KAAK,WAAW,GAAG;AACnD,uBAAO,MAAM,CAAC;AAAA,cAChB;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,eAAe,SAAS;AACtB,gBAAI,QAAQ,GAAG,GAAG,KAAK,MAAM,QAAQ;AACrC,gBAAI,QAAQ,WAAW,GAAG;AACxB,qBAAO,OAAO,MAAM,CAAC;AAAA,YACvB;AACA,gBAAI,QAAQ,WAAW,GAAG;AACxB,qBAAO,QAAQ,CAAC;AAAA,YAClB;AACA,qBAAS;AACT,iBAAK,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAC9C,uBAAS,QAAQ,CAAC;AAClB,wBAAU,OAAO;AAAA,YACnB;AACA,qBAAS,OAAO,MAAM,MAAM;AAC5B,qBAAS;AACT,iBAAK,IAAI,GAAG,OAAO,QAAQ,QAAQ,IAAI,MAAM,KAAK;AAChD,uBAAS,QAAQ,CAAC;AAClB,qBAAO,KAAK,QAAQ,MAAM;AAC1B,wBAAU,OAAO;AAAA,YACnB;AACA,mBAAO;AAAA,UACT;AAAA,QAEF;AAAC;AAID,QAAAA,gBAAe,UAAU,qBAAqB;AAI9C,QAAAA,gBAAe,UAAU,aAAa;AAItC,QAAAA,gBAAe,UAAU,WAAW;AAIpC,QAAAA,gBAAe,UAAU,eAAe;AAIxC,QAAAA,gBAAe,UAAU,eAAe;AAIxC,QAAAA,gBAAe,UAAU,SAAS;AAIlC,QAAAA,gBAAe,UAAU,UAAU;AAInC,QAAAA,gBAAe,UAAU,SAAS;AAGlC,QAAAA,gBAAe,UAAU,SAAS;AAGlC,QAAAA,gBAAe,SAAS;AAKxB,QAAAA,gBAAe,UAAU,SAAS;AAKlC,QAAAA,gBAAe,SAAS;AAIxB,QAAAA,gBAAe,UAAU,mBAAmB;AAI5C,QAAAA,gBAAe,mBAAmB;AAGlC,QAAAA,gBAAe,UAAU,UAAU;AAGnC,QAAAA,gBAAe,UAAU;AAGzB,QAAAA,gBAAe,UAAU,OAAO;AAGhC,QAAAA,gBAAe,OAAO;AAOtB,QAAAA,gBAAe,UAAU,kBAAkB,KAAK;AAOhD,QAAAA,gBAAe,UAAU,mBAAmB,MAAM;AAQlD,QAAAA,gBAAe,UAAU,gBAAgB;AAMzC,QAAAA,gBAAe,UAAU,qBAAqB;AAAA,UAC5C,SAAS;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAOA,QAAAA,gBAAe,UAAU,qBAAqB;AAAA,UAC5C,kBAAkB;AAAA,UAClB,mBAAmB;AAAA,UACnB,kCAAkC;AAAA,UAClC,iCAAiC;AAAA,UACjC,YAAY;AAAA,UACZ,kBAAkB;AAAA,UAClB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,qBAAqB;AAAA,UACrB,SAAS;AAAA,UACT,KAAK;AAAA,QACP;AAOA,QAAAA,gBAAe,UAAU,kBAAkB;AAAA,UACzC,cAAc;AAAA,UACd,eAAe;AAAA,QACjB;AAGA,QAAAA,gBAAe,UAAU,aAAa,gBAAgB,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,aAAkB,QAAQ,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAE7I,eAAOA;AAAA,MAET,GAAG,KAAK,IAAI;AAGZ,aAAO,UAAU;AAKjB,qBAAe,iBAAiB;AAKhC,sBAAgB,MAAM,sBAAsB,MAAM;AAAA;AAAA,QAEhD,cAAc;AACZ,gBAAM;AAAA,QACR;AAAA,MAEF;AAGA,qBAAe,gBAAgB;AAG/B,0BAAoB,MAAM,0BAA0B,MAAM;AAAA;AAAA,QAExD,cAAc;AACZ,gBAAM;AAAA,QACR;AAAA,MAEF;AAGA,0BAAoB,MAAM,0BAA0B,MAAM;AAAA,MAAC;AAE3D,qBAAe,oBAAoB;AAGnC,qBAAe,MAAM,qBAAqB,MAAM;AAAA;AAAA,QAE9C,cAAc;AACZ,gBAAM;AAAA,QACR;AAAA,MAEF;AAGA,qBAAe,cAAc;AAE7B,oBAAc,MAAM,oBAAoB,MAAM;AAAA;AAAA,QAE5C,cAAc;AACZ,gBAAM;AAAA,QACR;AAAA,MAEF;AAEA,uBAAiB,WAAW;AAAA,QAE1B,MAAME,eAAc;AAAA;AAAA;AAAA;AAAA,UAKlB,YAAY,MAAM;AAChB,iBAAK,OAAO;AACZ,iBAAK,SAAS;AACd,iBAAK,gBAAgB;AACrB,iBAAK,mBAAmB;AACxB,iBAAK,SAAS;AACd,iBAAK,QAAQ;AAAA,UACf;AAAA,QAEF;AAAC;AAMD,QAAAA,eAAc,UAAU,UAAU;AAGlC,QAAAA,eAAc,UAAU,aAAa;AAGrC,QAAAA,eAAc,UAAU,SAAS;AAIjC,QAAAA,eAAc,UAAU,SAAS;AAIjC,QAAAA,eAAc,UAAU,mBAAmB;AAI3C,QAAAA,eAAc,UAAU,QAAQ;AAEhC,eAAOA;AAAA,MAET,GAAG,KAAK,IAAI;AAGZ,qBAAe,gBAAgB;AAG/B,6BAAuB,MAAM,6BAA6B,0BAA0B;AAAA;AAAA;AAAA;AAAA,QAIlF,YAAY,SAAS;AACnB,gBAAM;AACN,eAAK,WAAW;AAChB,eAAK,OAAO;AAAA,QACd;AAAA;AAAA;AAAA;AAAA,QAMA,SAAS;AACP,eAAK,eAAe;AACpB,eAAK,QAAQ;AACb,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,SAAS,MAAM;AACb,cAAI,MAAM,GAAG,GAAG,GAAG,QAAQ,KAAK,MAAM;AACtC,cAAI,OAAO,SAAS,eAAe,SAAS,MAAM;AAChD;AAAA,UACF;AACA,cAAI,OAAO,SAAS,UAAU;AAE5B,gBAAI,KAAK,WAAW,GAAG;AACrB,mBAAK,eAAe;AAAA,YACtB;AACA,iBAAK,QAAQ,OAAO,KAAK,MAAM,MAAM;AAAA,UACvC,WAAW,OAAO,SAAS,IAAI,GAAG;AAEhC,iBAAK,QAAQ;AAAA,UACf,WAAW,gBAAgB,aAAa;AAEtC,mBAAO,OAAO,MAAM,KAAK,UAAU;AACnC,mBAAO,IAAI,WAAW,IAAI;AAC1B,iBAAK,IAAI,IAAI,GAAG,MAAM,KAAK,YAAa,KAAK,MAAM,IAAI,MAAM,IAAI,KAAM,IAAI,KAAK,MAAM,EAAE,IAAI,EAAE,GAAG;AAC/F,mBAAK,CAAC,IAAI,KAAK,CAAC;AAAA,YAClB;AACA,iBAAK,QAAQ;AAAA,UACf,WAAW,KAAK,UAAU,KAAK,kBAAkB,aAAa;AAE5D,mBAAO,OAAO,MAAM,KAAK,UAAU;AACnC,qBAAS,KAAK;AACd,mBAAO,IAAI,WAAW,KAAK,MAAM;AACjC,iBAAK,IAAI,IAAI,GAAG,OAAO,KAAK,YAAa,KAAK,OAAO,IAAI,OAAO,IAAI,MAAO,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,GAAG;AACpG,mBAAK,CAAC,IAAI,KAAK,IAAI,MAAM;AAAA,YAC3B;AACA,iBAAK,QAAQ;AAAA,UACf,OAAO;AAIL,kBAAM,IAAI,MAAM,2BAA2B,IAAI,EAAE;AAAA,UACnD;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAaA,iBAAiB,SAAS,gBAAgB;AACxC,cAAI,KAAK,cAAc;AACrB,gBAAI,EAAE,kBAAkB,iBAAiB;AACvC,sBAAQ,cAAc,IAAI,KAAK;AAAA,YACjC;AAAA,UACF;AACA,cAAI,KAAK,OAAO;AAGd,oBAAQ,gBAAgB,IAAI,KAAK,MAAM,OAAO,SAAS;AAAA,UACzD;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,aAAa,SAAS;AACpB,cAAI,KAAK,OAAO;AACd,oBAAQ,MAAM,KAAK,KAAK;AAAA,UAC1B;AACA,kBAAQ,IAAI;AACZ,iBAAO;AAAA,QACT;AAAA,MAEF;AAGA,qBAAe,uBAAuB;AAAA,IAExC,GAAG,KAAK,OAAI;AAAA;AAAA;", "names": ["XMLHttpRequestEventTarget", "XMLHttpRequest", "url", "ProgressEvent"]}