import {
  CommonModule
} from "./chunk-AJ2TM24F.js";
import {
  Directive,
  ElementRef,
  HostBinding,
  Input,
  NgModule,
  Renderer2,
  setClassMetadata,
  ɵɵclassProp,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject
} from "./chunk-2AFYHOAX.js";

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-layout.mjs
var StackDirective = class {
  constructor(render, hostElement) {
    this.render = render;
    this.hostElement = hostElement;
    this.cdsStack = "vertical";
  }
  get isHorizontal() {
    return this.cdsStack === "horizontal";
  }
  get isVertical() {
    return this.cdsStack === "vertical" || !this.cdsStack;
  }
  /**
   * @deprecated as of v5 - Use `cdsStack` input property instead
   */
  set ibmStack(type) {
    this.cdsStack = type;
  }
  /**
   * Gap in the layout, provide a custom value (string) or a step from the spacing scale (number)
   */
  set gap(num) {
    if (num !== void 0) {
      this.render.removeClass(this.hostElement.nativeElement, `cds--stack-scale-${this._gap}`);
      this.render.addClass(this.hostElement.nativeElement, `cds--stack-scale-${num}`);
      this._gap = num;
    }
  }
};
StackDirective.ɵfac = function StackDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || StackDirective)(ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ElementRef));
};
StackDirective.ɵdir = ɵɵdefineDirective({
  type: StackDirective,
  selectors: [["", "cdsStack", ""], ["", "ibmStack", ""]],
  hostVars: 4,
  hostBindings: function StackDirective_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--stack-horizontal", ctx.isHorizontal)("cds--stack-vertical", ctx.isVertical);
    }
  },
  inputs: {
    ibmStack: "ibmStack",
    cdsStack: "cdsStack",
    gap: "gap"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StackDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsStack], [ibmStack]"
    }]
  }], function() {
    return [{
      type: Renderer2
    }, {
      type: ElementRef
    }];
  }, {
    isHorizontal: [{
      type: HostBinding,
      args: ["class.cds--stack-horizontal"]
    }],
    isVertical: [{
      type: HostBinding,
      args: ["class.cds--stack-vertical"]
    }],
    ibmStack: [{
      type: Input
    }],
    cdsStack: [{
      type: Input
    }],
    gap: [{
      type: Input
    }]
  });
})();
var LayoutModule = class {
};
LayoutModule.ɵfac = function LayoutModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || LayoutModule)();
};
LayoutModule.ɵmod = ɵɵdefineNgModule({
  type: LayoutModule,
  declarations: [StackDirective],
  imports: [CommonModule],
  exports: [StackDirective]
});
LayoutModule.ɵinj = ɵɵdefineInjector({
  imports: [CommonModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LayoutModule, [{
    type: NgModule,
    args: [{
      declarations: [StackDirective],
      exports: [StackDirective],
      imports: [CommonModule]
    }]
  }], null, null);
})();
export {
  LayoutModule,
  StackDirective
};
//# sourceMappingURL=carbon-components-angular_layout.js.map
