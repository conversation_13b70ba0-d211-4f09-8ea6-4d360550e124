{"version": 3, "sources": ["../../../../../../node_modules/@angular/ssr/fesm2022/node.mjs"], "sourcesContent": ["import { renderApplication, renderModule, ɵSERVER_CONTEXT as _SERVER_CONTEXT } from '@angular/platform-server';\nimport * as fs from 'node:fs';\nimport { dirname, join, normalize, resolve } from 'node:path';\nimport { URL as URL$1, fileURLToPath } from 'node:url';\nimport { ɵInlineCriticalCssProcessor as _InlineCriticalCssProcessor, AngularAppEngine } from '@angular/ssr';\nimport { readFile } from 'node:fs/promises';\nimport { argv } from 'node:process';\n\n/**\n * Attaches listeners to the Node.js process to capture and handle unhandled rejections and uncaught exceptions.\n * Captured errors are logged to the console. This function logs errors to the console, preventing unhandled errors\n * from crashing the server. It is particularly useful for Zoneless apps, ensuring error handling without relying on Zone.js.\n *\n * @remarks\n * This function is a no-op if zone.js is available.\n * For Zone-based apps, similar functionality is provided by Zone.js itself. See the Zone.js implementation here:\n * https://github.com/angular/angular/blob/4a8d0b79001ec09bcd6f2d6b15117aa6aac1932c/packages/zone.js/lib/node/node.ts#L94%7C\n *\n * @internal\n */\nfunction attachNodeGlobalErrorHandlers() {\n    if (typeof Zone !== 'undefined') {\n        return;\n    }\n    // Ensure that the listeners are registered only once.\n    // Otherwise, multiple instances may be registered during edit/refresh.\n    const gThis = globalThis;\n    if (gThis.ngAttachNodeGlobalErrorHandlersCalled) {\n        return;\n    }\n    gThis.ngAttachNodeGlobalErrorHandlersCalled = true;\n    process\n        // eslint-disable-next-line no-console\n        .on('unhandledRejection', (error) => console.error('unhandledRejection', error))\n        // eslint-disable-next-line no-console\n        .on('uncaughtException', (error) => console.error('uncaughtException', error));\n}\n\nclass CommonEngineInlineCriticalCssProcessor {\n    resourceCache = new Map();\n    async process(html, outputPath) {\n        const beasties = new _InlineCriticalCssProcessor(async (path) => {\n            let resourceContent = this.resourceCache.get(path);\n            if (resourceContent === undefined) {\n                resourceContent = await readFile(path, 'utf-8');\n                this.resourceCache.set(path, resourceContent);\n            }\n            return resourceContent;\n        }, outputPath);\n        return beasties.process(html);\n    }\n}\n\nconst PERFORMANCE_MARK_PREFIX = '🅰️';\nfunction printPerformanceLogs() {\n    let maxWordLength = 0;\n    const benchmarks = [];\n    for (const { name, duration } of performance.getEntriesByType('measure')) {\n        if (!name.startsWith(PERFORMANCE_MARK_PREFIX)) {\n            continue;\n        }\n        // `🅰️:Retrieve SSG Page` -> `Retrieve SSG Page:`\n        const step = name.slice(PERFORMANCE_MARK_PREFIX.length + 1) + ':';\n        if (step.length > maxWordLength) {\n            maxWordLength = step.length;\n        }\n        benchmarks.push([step, `${duration.toFixed(1)}ms`]);\n        performance.clearMeasures(name);\n    }\n    /* eslint-disable no-console */\n    console.log('********** Performance results **********');\n    for (const [step, value] of benchmarks) {\n        const spaces = maxWordLength - step.length + 5;\n        console.log(step + ' '.repeat(spaces) + value);\n    }\n    console.log('*****************************************');\n    /* eslint-enable no-console */\n}\nasync function runMethodAndMeasurePerf(label, asyncMethod) {\n    const labelName = `${PERFORMANCE_MARK_PREFIX}:${label}`;\n    const startLabel = `start:${labelName}`;\n    const endLabel = `end:${labelName}`;\n    try {\n        performance.mark(startLabel);\n        return await asyncMethod();\n    }\n    finally {\n        performance.mark(endLabel);\n        performance.measure(labelName, startLabel, endLabel);\n        performance.clearMarks(startLabel);\n        performance.clearMarks(endLabel);\n    }\n}\nfunction noopRunMethodAndMeasurePerf(label, asyncMethod) {\n    return asyncMethod();\n}\n\nconst SSG_MARKER_REGEXP = /ng-server-context=[\"']\\w*\\|?ssg\\|?\\w*[\"']/;\n/**\n * A common engine to use to server render an application.\n */\nclass CommonEngine {\n    options;\n    templateCache = new Map();\n    inlineCriticalCssProcessor = new CommonEngineInlineCriticalCssProcessor();\n    pageIsSSG = new Map();\n    constructor(options) {\n        this.options = options;\n        attachNodeGlobalErrorHandlers();\n    }\n    /**\n     * Render an HTML document for a specific URL with specified\n     * render options\n     */\n    async render(opts) {\n        const enablePerformanceProfiler = this.options?.enablePerformanceProfiler;\n        const runMethod = enablePerformanceProfiler\n            ? runMethodAndMeasurePerf\n            : noopRunMethodAndMeasurePerf;\n        let html = await runMethod('Retrieve SSG Page', () => this.retrieveSSGPage(opts));\n        if (html === undefined) {\n            html = await runMethod('Render Page', () => this.renderApplication(opts));\n            if (opts.inlineCriticalCss !== false) {\n                const content = await runMethod('Inline Critical CSS', () => \n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                this.inlineCriticalCss(html, opts));\n                html = content;\n            }\n        }\n        if (enablePerformanceProfiler) {\n            printPerformanceLogs();\n        }\n        return html;\n    }\n    inlineCriticalCss(html, opts) {\n        const outputPath = opts.publicPath ?? (opts.documentFilePath ? dirname(opts.documentFilePath) : '');\n        return this.inlineCriticalCssProcessor.process(html, outputPath);\n    }\n    async retrieveSSGPage(opts) {\n        const { publicPath, documentFilePath, url } = opts;\n        if (!publicPath || !documentFilePath || url === undefined) {\n            return undefined;\n        }\n        const { pathname } = new URL$1(url, 'resolve://');\n        // Do not use `resolve` here as otherwise it can lead to path traversal vulnerability.\n        // See: https://portswigger.net/web-security/file-path-traversal\n        const pagePath = join(publicPath, pathname, 'index.html');\n        if (this.pageIsSSG.get(pagePath)) {\n            // Serve pre-rendered page.\n            return fs.promises.readFile(pagePath, 'utf-8');\n        }\n        if (!pagePath.startsWith(normalize(publicPath))) {\n            // Potential path traversal detected.\n            return undefined;\n        }\n        if (pagePath === resolve(documentFilePath) || !(await exists(pagePath))) {\n            // View matches with prerender path or file does not exist.\n            this.pageIsSSG.set(pagePath, false);\n            return undefined;\n        }\n        // Static file exists.\n        const content = await fs.promises.readFile(pagePath, 'utf-8');\n        const isSSG = SSG_MARKER_REGEXP.test(content);\n        this.pageIsSSG.set(pagePath, isSSG);\n        return isSSG ? content : undefined;\n    }\n    async renderApplication(opts) {\n        const moduleOrFactory = this.options?.bootstrap ?? opts.bootstrap;\n        if (!moduleOrFactory) {\n            throw new Error('A module or bootstrap option must be provided.');\n        }\n        const extraProviders = [\n            { provide: _SERVER_CONTEXT, useValue: 'ssr' },\n            ...(opts.providers ?? []),\n            ...(this.options?.providers ?? []),\n        ];\n        let document = opts.document;\n        if (!document && opts.documentFilePath) {\n            document = await this.getDocument(opts.documentFilePath);\n        }\n        const commonRenderingOptions = {\n            url: opts.url,\n            document,\n        };\n        return isBootstrapFn(moduleOrFactory)\n            ? renderApplication(moduleOrFactory, {\n                platformProviders: extraProviders,\n                ...commonRenderingOptions,\n            })\n            : renderModule(moduleOrFactory, { extraProviders, ...commonRenderingOptions });\n    }\n    /** Retrieve the document from the cache or the filesystem */\n    async getDocument(filePath) {\n        let doc = this.templateCache.get(filePath);\n        if (!doc) {\n            doc = await fs.promises.readFile(filePath, 'utf-8');\n            this.templateCache.set(filePath, doc);\n        }\n        return doc;\n    }\n}\nasync function exists(path) {\n    try {\n        await fs.promises.access(path, fs.constants.F_OK);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nfunction isBootstrapFn(value) {\n    // We can differentiate between a module and a bootstrap function by reading compiler-generated `ɵmod` static property:\n    return typeof value === 'function' && !('ɵmod' in value);\n}\n\n/**\n * A set containing all the pseudo-headers defined in the HTTP/2 specification.\n *\n * This set can be used to filter out pseudo-headers from a list of headers,\n * as they are not allowed to be set directly using the `Node.js` Undici API or\n * the web `Headers` API.\n */\nconst HTTP2_PSEUDO_HEADERS = new Set([':method', ':scheme', ':authority', ':path', ':status']);\n/**\n * Converts a Node.js `IncomingMessage` or `Http2ServerRequest` into a\n * Web Standard `Request` object.\n *\n * This function adapts the Node.js request objects to a format that can\n * be used by web platform APIs.\n *\n * @param nodeRequest - The Node.js request object (`IncomingMessage` or `Http2ServerRequest`) to convert.\n * @returns A Web Standard `Request` object.\n */\nfunction createWebRequestFromNodeRequest(nodeRequest) {\n    const { headers, method = 'GET' } = nodeRequest;\n    const withBody = method !== 'GET' && method !== 'HEAD';\n    const referrer = headers.referer && URL.canParse(headers.referer) ? headers.referer : undefined;\n    return new Request(createRequestUrl(nodeRequest), {\n        method,\n        headers: createRequestHeaders(headers),\n        body: withBody ? nodeRequest : undefined,\n        duplex: withBody ? 'half' : undefined,\n        referrer,\n    });\n}\n/**\n * Creates a `Headers` object from Node.js `IncomingHttpHeaders`.\n *\n * @param nodeHeaders - The Node.js `IncomingHttpHeaders` object to convert.\n * @returns A `Headers` object containing the converted headers.\n */\nfunction createRequestHeaders(nodeHeaders) {\n    const headers = new Headers();\n    for (const [name, value] of Object.entries(nodeHeaders)) {\n        if (HTTP2_PSEUDO_HEADERS.has(name)) {\n            continue;\n        }\n        if (typeof value === 'string') {\n            headers.append(name, value);\n        }\n        else if (Array.isArray(value)) {\n            for (const item of value) {\n                headers.append(name, item);\n            }\n        }\n    }\n    return headers;\n}\n/**\n * Creates a `URL` object from a Node.js `IncomingMessage`, taking into account the protocol, host, and port.\n *\n * @param nodeRequest - The Node.js `IncomingMessage` or `Http2ServerRequest` object to extract URL information from.\n * @returns A `URL` object representing the request URL.\n */\nfunction createRequestUrl(nodeRequest) {\n    const { headers, socket, url = '', originalUrl, } = nodeRequest;\n    const protocol = getFirstHeaderValue(headers['x-forwarded-proto']) ??\n        ('encrypted' in socket && socket.encrypted ? 'https' : 'http');\n    const hostname = getFirstHeaderValue(headers['x-forwarded-host']) ?? headers.host ?? headers[':authority'];\n    if (Array.isArray(hostname)) {\n        throw new Error('host value cannot be an array.');\n    }\n    let hostnameWithPort = hostname;\n    if (!hostname?.includes(':')) {\n        const port = getFirstHeaderValue(headers['x-forwarded-port']);\n        if (port) {\n            hostnameWithPort += `:${port}`;\n        }\n    }\n    return new URL(originalUrl ?? url, `${protocol}://${hostnameWithPort}`);\n}\n/**\n * Extracts the first value from a multi-value header string.\n *\n * @param value - A string or an array of strings representing the header values.\n *                           If it's a string, values are expected to be comma-separated.\n * @returns The first trimmed value from the multi-value header, or `undefined` if the input is invalid or empty.\n *\n * @example\n * ```typescript\n * getFirstHeaderValue(\"value1, value2, value3\"); // \"value1\"\n * getFirstHeaderValue([\"value1\", \"value2\"]); // \"value1\"\n * getFirstHeaderValue(undefined); // undefined\n * ```\n */\nfunction getFirstHeaderValue(value) {\n    return value?.toString().split(',', 1)[0]?.trim();\n}\n\n/**\n * Angular server application engine.\n * Manages Angular server applications (including localized ones), handles rendering requests,\n * and optionally transforms index HTML before rendering.\n *\n * @remarks This class should be instantiated once and used as a singleton across the server-side\n * application to ensure consistent handling of rendering requests and resource management.\n */\nclass AngularNodeAppEngine {\n    angularAppEngine = new AngularAppEngine();\n    constructor() {\n        attachNodeGlobalErrorHandlers();\n    }\n    /**\n     * Handles an incoming HTTP request by serving prerendered content, performing server-side rendering,\n     * or delivering a static file for client-side rendered routes based on the `RenderMode` setting.\n     *\n     * This method adapts Node.js's `IncomingMessage` or `Http2ServerRequest`\n     * to a format compatible with the `AngularAppEngine` and delegates the handling logic to it.\n     *\n     * @param request - The incoming HTTP request (`IncomingMessage` or `Http2ServerRequest`).\n     * @param requestContext - Optional context for rendering, such as metadata associated with the request.\n     * @returns A promise that resolves to the resulting HTTP response object, or `null` if no matching Angular route is found.\n     *\n     * @remarks A request to `https://www.example.com/page/index.html` will serve or render the Angular route\n     * corresponding to `https://www.example.com/page`.\n     */\n    async handle(request, requestContext) {\n        const webRequest = createWebRequestFromNodeRequest(request);\n        return this.angularAppEngine.handle(webRequest, requestContext);\n    }\n}\n\n/**\n * Attaches metadata to the handler function to mark it as a special handler for Node.js environments.\n *\n * @typeParam T - The type of the handler function.\n * @param handler - The handler function to be defined and annotated.\n * @returns The same handler function passed as an argument, with metadata attached.\n *\n * @example\n * Usage in an Express application:\n * ```ts\n * const app = express();\n * export default createNodeRequestHandler(app);\n * ```\n *\n * @example\n * Usage in a Hono application:\n * ```ts\n * const app = new Hono();\n * export default createNodeRequestHandler(async (req, res, next) => {\n *   try {\n *     const webRes = await app.fetch(createWebRequestFromNodeRequest(req));\n *     if (webRes) {\n *       await writeResponseToNodeResponse(webRes, res);\n *     } else {\n *       next();\n *     }\n *   } catch (error) {\n *     next(error);\n *   }\n * }));\n * ```\n *\n * @example\n * Usage in a Fastify application:\n * ```ts\n * const app = Fastify();\n * export default createNodeRequestHandler(async (req, res) => {\n *   await app.ready();\n *   app.server.emit('request', req, res);\n *   res.send('Hello from Fastify with Node Next Handler!');\n * }));\n * ```\n */\nfunction createNodeRequestHandler(handler) {\n    handler['__ng_node_request_handler__'] = true;\n    return handler;\n}\n\n/**\n * Streams a web-standard `Response` into a Node.js `ServerResponse`\n * or `Http2ServerResponse`.\n *\n * This function adapts the web `Response` object to write its content\n * to a Node.js response object, handling both HTTP/1.1 and HTTP/2.\n *\n * @param source - The web-standard `Response` object to stream from.\n * @param destination - The Node.js response object (`ServerResponse` or `Http2ServerResponse`) to stream into.\n * @returns A promise that resolves once the streaming operation is complete.\n */\nasync function writeResponseToNodeResponse(source, destination) {\n    const { status, headers, body } = source;\n    destination.statusCode = status;\n    let cookieHeaderSet = false;\n    for (const [name, value] of headers.entries()) {\n        if (name === 'set-cookie') {\n            if (cookieHeaderSet) {\n                continue;\n            }\n            // Sets the 'set-cookie' header only once to ensure it is correctly applied.\n            // Concatenating 'set-cookie' values can lead to incorrect behavior, so we use a single value from `headers.getSetCookie()`.\n            destination.setHeader(name, headers.getSetCookie());\n            cookieHeaderSet = true;\n        }\n        else {\n            destination.setHeader(name, value);\n        }\n    }\n    if ('flushHeaders' in destination) {\n        destination.flushHeaders();\n    }\n    if (!body) {\n        destination.end();\n        return;\n    }\n    try {\n        const reader = body.getReader();\n        destination.on('close', () => {\n            reader.cancel().catch((error) => {\n                // eslint-disable-next-line no-console\n                console.error(`An error occurred while writing the response body for: ${destination.req.url}.`, error);\n            });\n        });\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n                destination.end();\n                break;\n            }\n            const canContinue = destination.write(value);\n            if (canContinue === false) {\n                // Explicitly check for `false`, as AWS may return `undefined` even though this is not valid.\n                // See: https://github.com/CodeGenieApp/serverless-express/issues/683\n                await new Promise((resolve) => destination.once('drain', resolve));\n            }\n        }\n    }\n    catch {\n        destination.end('Internal server error.');\n    }\n}\n\n/**\n * Determines whether the provided URL represents the main entry point module.\n *\n * This function checks if the provided URL corresponds to the main ESM module being executed directly.\n * It's useful for conditionally executing code that should only run when a module is the entry point,\n * such as starting a server or initializing an application.\n *\n * It performs two key checks:\n * 1. Verifies if the URL starts with 'file:', ensuring it is a local file.\n * 2. Compares the URL's resolved file path with the first command-line argument (`process.argv[1]`),\n *    which points to the file being executed.\n *\n * @param url The URL of the module to check. This should typically be `import.meta.url`.\n * @returns `true` if the provided URL represents the main entry point, otherwise `false`.\n */\nfunction isMainModule(url) {\n    return url.startsWith('file:') && argv[1] === fileURLToPath(url);\n}\n\nexport { AngularNodeAppEngine, CommonEngine, createNodeRequestHandler, createWebRequestFromNodeRequest, isMainModule, writeResponseToNodeResponse };\n\n"], "mappings": ";;;;;;;;;;;;;;;;AACA,YAAY,QAAQ;AACpB,SAAS,SAAS,MAAM,WAAW,eAAe;AAClD,SAAS,OAAO,OAAO,qBAAqB;AAE5C,SAAS,gBAAgB;AACzB,SAAS,YAAY;AAcrB,SAAS,gCAAgC;AACrC,MAAI,OAAO,SAAS,aAAa;AAC7B;AAAA,EACJ;AAGA,QAAM,QAAQ;AACd,MAAI,MAAM,uCAAuC;AAC7C;AAAA,EACJ;AACA,QAAM,wCAAwC;AAC9C,UAEK,GAAG,sBAAsB,CAAC,UAAU,QAAQ,MAAM,sBAAsB,KAAK,CAAC,EAE9E,GAAG,qBAAqB,CAAC,UAAU,QAAQ,MAAM,qBAAqB,KAAK,CAAC;AACrF;AAEA,IAAM,yCAAN,MAA6C;AAAA,EACzC,gBAAgB,oBAAI,IAAI;AAAA,EACxB,MAAM,QAAQ,MAAM,YAAY;AAC5B,UAAM,WAAW,IAAI,2BAA4B,OAAO,SAAS;AAC7D,UAAI,kBAAkB,KAAK,cAAc,IAAI,IAAI;AACjD,UAAI,oBAAoB,QAAW;AAC/B,0BAAkB,MAAM,SAAS,MAAM,OAAO;AAC9C,aAAK,cAAc,IAAI,MAAM,eAAe;AAAA,MAChD;AACA,aAAO;AAAA,IACX,GAAG,UAAU;AACb,WAAO,SAAS,QAAQ,IAAI;AAAA,EAChC;AACJ;AAEA,IAAM,0BAA0B;AAChC,SAAS,uBAAuB;AAC5B,MAAI,gBAAgB;AACpB,QAAM,aAAa,CAAC;AACpB,aAAW,EAAE,MAAM,SAAS,KAAK,YAAY,iBAAiB,SAAS,GAAG;AACtE,QAAI,CAAC,KAAK,WAAW,uBAAuB,GAAG;AAC3C;AAAA,IACJ;AAEA,UAAM,OAAO,KAAK,MAAM,wBAAwB,SAAS,CAAC,IAAI;AAC9D,QAAI,KAAK,SAAS,eAAe;AAC7B,sBAAgB,KAAK;AAAA,IACzB;AACA,eAAW,KAAK,CAAC,MAAM,GAAG,SAAS,QAAQ,CAAC,CAAC,IAAI,CAAC;AAClD,gBAAY,cAAc,IAAI;AAAA,EAClC;AAEA,UAAQ,IAAI,2CAA2C;AACvD,aAAW,CAAC,MAAM,KAAK,KAAK,YAAY;AACpC,UAAM,SAAS,gBAAgB,KAAK,SAAS;AAC7C,YAAQ,IAAI,OAAO,IAAI,OAAO,MAAM,IAAI,KAAK;AAAA,EACjD;AACA,UAAQ,IAAI,2CAA2C;AAE3D;AACA,eAAe,wBAAwB,OAAO,aAAa;AACvD,QAAM,YAAY,GAAG,uBAAuB,IAAI,KAAK;AACrD,QAAM,aAAa,SAAS,SAAS;AACrC,QAAM,WAAW,OAAO,SAAS;AACjC,MAAI;AACA,gBAAY,KAAK,UAAU;AAC3B,WAAO,MAAM,YAAY;AAAA,EAC7B,UACA;AACI,gBAAY,KAAK,QAAQ;AACzB,gBAAY,QAAQ,WAAW,YAAY,QAAQ;AACnD,gBAAY,WAAW,UAAU;AACjC,gBAAY,WAAW,QAAQ;AAAA,EACnC;AACJ;AACA,SAAS,4BAA4B,OAAO,aAAa;AACrD,SAAO,YAAY;AACvB;AAEA,IAAM,oBAAoB;AAI1B,IAAM,eAAN,MAAmB;AAAA,EACf;AAAA,EACA,gBAAgB,oBAAI,IAAI;AAAA,EACxB,6BAA6B,IAAI,uCAAuC;AAAA,EACxE,YAAY,oBAAI,IAAI;AAAA,EACpB,YAAY,SAAS;AACjB,SAAK,UAAU;AACf,kCAA8B;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,MAAM;AACf,UAAM,4BAA4B,KAAK,SAAS;AAChD,UAAM,YAAY,4BACZ,0BACA;AACN,QAAI,OAAO,MAAM,UAAU,qBAAqB,MAAM,KAAK,gBAAgB,IAAI,CAAC;AAChF,QAAI,SAAS,QAAW;AACpB,aAAO,MAAM,UAAU,eAAe,MAAM,KAAK,kBAAkB,IAAI,CAAC;AACxE,UAAI,KAAK,sBAAsB,OAAO;AAClC,cAAM,UAAU,MAAM,UAAU,uBAAuB;AAAA;AAAA,UAEvD,KAAK,kBAAkB,MAAM,IAAI;AAAA,SAAC;AAClC,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,2BAA2B;AAC3B,2BAAqB;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,MAAM,MAAM;AAC1B,UAAM,aAAa,KAAK,eAAe,KAAK,mBAAmB,QAAQ,KAAK,gBAAgB,IAAI;AAChG,WAAO,KAAK,2BAA2B,QAAQ,MAAM,UAAU;AAAA,EACnE;AAAA,EACA,MAAM,gBAAgB,MAAM;AACxB,UAAM,EAAE,YAAY,kBAAkB,IAAI,IAAI;AAC9C,QAAI,CAAC,cAAc,CAAC,oBAAoB,QAAQ,QAAW;AACvD,aAAO;AAAA,IACX;AACA,UAAM,EAAE,SAAS,IAAI,IAAI,MAAM,KAAK,YAAY;AAGhD,UAAM,WAAW,KAAK,YAAY,UAAU,YAAY;AACxD,QAAI,KAAK,UAAU,IAAI,QAAQ,GAAG;AAE9B,aAAU,YAAS,SAAS,UAAU,OAAO;AAAA,IACjD;AACA,QAAI,CAAC,SAAS,WAAW,UAAU,UAAU,CAAC,GAAG;AAE7C,aAAO;AAAA,IACX;AACA,QAAI,aAAa,QAAQ,gBAAgB,KAAK,CAAE,MAAM,OAAO,QAAQ,GAAI;AAErE,WAAK,UAAU,IAAI,UAAU,KAAK;AAClC,aAAO;AAAA,IACX;AAEA,UAAM,UAAU,MAAS,YAAS,SAAS,UAAU,OAAO;AAC5D,UAAM,QAAQ,kBAAkB,KAAK,OAAO;AAC5C,SAAK,UAAU,IAAI,UAAU,KAAK;AAClC,WAAO,QAAQ,UAAU;AAAA,EAC7B;AAAA,EACA,MAAM,kBAAkB,MAAM;AAC1B,UAAM,kBAAkB,KAAK,SAAS,aAAa,KAAK;AACxD,QAAI,CAAC,iBAAiB;AAClB,YAAM,IAAI,MAAM,gDAAgD;AAAA,IACpE;AACA,UAAM,iBAAiB;AAAA,MACnB,EAAE,SAAS,gBAAiB,UAAU,MAAM;AAAA,MAC5C,GAAI,KAAK,aAAa,CAAC;AAAA,MACvB,GAAI,KAAK,SAAS,aAAa,CAAC;AAAA,IACpC;AACA,QAAI,WAAW,KAAK;AACpB,QAAI,CAAC,YAAY,KAAK,kBAAkB;AACpC,iBAAW,MAAM,KAAK,YAAY,KAAK,gBAAgB;AAAA,IAC3D;AACA,UAAM,yBAAyB;AAAA,MAC3B,KAAK,KAAK;AAAA,MACV;AAAA,IACJ;AACA,WAAO,cAAc,eAAe,IAC9B,kBAAkB,iBAAiB;AAAA,MACjC,mBAAmB;AAAA,OAChB,uBACN,IACC,aAAa,iBAAiB,iBAAE,kBAAmB,uBAAwB;AAAA,EACrF;AAAA;AAAA,EAEA,MAAM,YAAY,UAAU;AACxB,QAAI,MAAM,KAAK,cAAc,IAAI,QAAQ;AACzC,QAAI,CAAC,KAAK;AACN,YAAM,MAAS,YAAS,SAAS,UAAU,OAAO;AAClD,WAAK,cAAc,IAAI,UAAU,GAAG;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AACJ;AACA,eAAe,OAAO,MAAM;AACxB,MAAI;AACA,UAAS,YAAS,OAAO,MAAS,aAAU,IAAI;AAChD,WAAO;AAAA,EACX,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACA,SAAS,cAAc,OAAO;AAE1B,SAAO,OAAO,UAAU,cAAc,EAAE,UAAU;AACtD;AASA,IAAM,uBAAuB,oBAAI,IAAI,CAAC,WAAW,WAAW,cAAc,SAAS,SAAS,CAAC;AAW7F,SAAS,gCAAgC,aAAa;AAClD,QAAM,EAAE,SAAS,SAAS,MAAM,IAAI;AACpC,QAAM,WAAW,WAAW,SAAS,WAAW;AAChD,QAAM,WAAW,QAAQ,WAAW,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACtF,SAAO,IAAI,QAAQ,iBAAiB,WAAW,GAAG;AAAA,IAC9C;AAAA,IACA,SAAS,qBAAqB,OAAO;AAAA,IACrC,MAAM,WAAW,cAAc;AAAA,IAC/B,QAAQ,WAAW,SAAS;AAAA,IAC5B;AAAA,EACJ,CAAC;AACL;AAOA,SAAS,qBAAqB,aAAa;AACvC,QAAM,UAAU,IAAI,QAAQ;AAC5B,aAAW,CAAC,MAAM,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAG;AACrD,QAAI,qBAAqB,IAAI,IAAI,GAAG;AAChC;AAAA,IACJ;AACA,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC9B,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,iBAAW,QAAQ,OAAO;AACtB,gBAAQ,OAAO,MAAM,IAAI;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAOA,SAAS,iBAAiB,aAAa;AACnC,QAAM,EAAE,SAAS,QAAQ,MAAM,IAAI,YAAa,IAAI;AACpD,QAAM,WAAW,oBAAoB,QAAQ,mBAAmB,CAAC,MAC5D,eAAe,UAAU,OAAO,YAAY,UAAU;AAC3D,QAAM,WAAW,oBAAoB,QAAQ,kBAAkB,CAAC,KAAK,QAAQ,QAAQ,QAAQ,YAAY;AACzG,MAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AACA,MAAI,mBAAmB;AACvB,MAAI,CAAC,UAAU,SAAS,GAAG,GAAG;AAC1B,UAAM,OAAO,oBAAoB,QAAQ,kBAAkB,CAAC;AAC5D,QAAI,MAAM;AACN,0BAAoB,IAAI,IAAI;AAAA,IAChC;AAAA,EACJ;AACA,SAAO,IAAI,IAAI,eAAe,KAAK,GAAG,QAAQ,MAAM,gBAAgB,EAAE;AAC1E;AAeA,SAAS,oBAAoB,OAAO;AAChC,SAAO,OAAO,SAAS,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK;AACpD;AAUA,IAAM,uBAAN,MAA2B;AAAA,EACvB,mBAAmB,IAAI,iBAAiB;AAAA,EACxC,cAAc;AACV,kCAA8B;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,OAAO,SAAS,gBAAgB;AAClC,UAAM,aAAa,gCAAgC,OAAO;AAC1D,WAAO,KAAK,iBAAiB,OAAO,YAAY,cAAc;AAAA,EAClE;AACJ;AA6CA,SAAS,yBAAyB,SAAS;AACvC,UAAQ,6BAA6B,IAAI;AACzC,SAAO;AACX;AAaA,eAAe,4BAA4B,QAAQ,aAAa;AAC5D,QAAM,EAAE,QAAQ,SAAS,KAAK,IAAI;AAClC,cAAY,aAAa;AACzB,MAAI,kBAAkB;AACtB,aAAW,CAAC,MAAM,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAC3C,QAAI,SAAS,cAAc;AACvB,UAAI,iBAAiB;AACjB;AAAA,MACJ;AAGA,kBAAY,UAAU,MAAM,QAAQ,aAAa,CAAC;AAClD,wBAAkB;AAAA,IACtB,OACK;AACD,kBAAY,UAAU,MAAM,KAAK;AAAA,IACrC;AAAA,EACJ;AACA,MAAI,kBAAkB,aAAa;AAC/B,gBAAY,aAAa;AAAA,EAC7B;AACA,MAAI,CAAC,MAAM;AACP,gBAAY,IAAI;AAChB;AAAA,EACJ;AACA,MAAI;AACA,UAAM,SAAS,KAAK,UAAU;AAC9B,gBAAY,GAAG,SAAS,MAAM;AAC1B,aAAO,OAAO,EAAE,MAAM,CAAC,UAAU;AAE7B,gBAAQ,MAAM,0DAA0D,YAAY,IAAI,GAAG,KAAK,KAAK;AAAA,MACzG,CAAC;AAAA,IACL,CAAC;AAED,WAAO,MAAM;AACT,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACN,oBAAY,IAAI;AAChB;AAAA,MACJ;AACA,YAAM,cAAc,YAAY,MAAM,KAAK;AAC3C,UAAI,gBAAgB,OAAO;AAGvB,cAAM,IAAI,QAAQ,CAACA,aAAY,YAAY,KAAK,SAASA,QAAO,CAAC;AAAA,MACrE;AAAA,IACJ;AAAA,EACJ,QACM;AACF,gBAAY,IAAI,wBAAwB;AAAA,EAC5C;AACJ;AAiBA,SAAS,aAAa,KAAK;AACvB,SAAO,IAAI,WAAW,OAAO,KAAK,KAAK,CAAC,MAAM,cAAc,GAAG;AACnE;", "names": ["resolve"]}