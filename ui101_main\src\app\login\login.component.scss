@use '@carbon/styles/scss/spacing' as spacing;
@use '@carbon/styles/scss/breakpoint' as breakpoint;
@use '@carbon/styles/scss/type' as type;
@use '@carbon/styles/scss/colors' as colors;

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: spacing.$spacing-05;
  background: linear-gradient(135deg, var(--cds-background) 0%, var(--cds-layer-01) 100%);
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
  padding: spacing.$spacing-07;
  background-color: var(--cds-layer-01);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--cds-border-subtle-01);

  @include breakpoint.breakpoint-up('md') {
    padding: spacing.$spacing-08;
    max-width: 420px;
  }
}

.login-title {
  @include type.type-style('heading-04');
  color: var(--cds-text-primary);
  margin-bottom: spacing.$spacing-07;
  text-align: center;
  font-weight: 600;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: spacing.$spacing-06;
}

.form-field {
  width: 100%;
}

.form-actions {
  margin-top: spacing.$spacing-06;
}

.login-button {
  width: 100%;
  justify-content: center;
  height: 48px;
  font-weight: 600;

  &:disabled {
    cursor: not-allowed;
  }
}
