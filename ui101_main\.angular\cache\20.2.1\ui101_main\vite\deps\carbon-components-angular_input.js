import {
  FormsModule
} from "./chunk-2PQHGHUQ.js";
import {
  BaseIconButton,
  ButtonModule,
  Tooltip,
  TooltipModule
} from "./chunk-WUGMS2PT.js";
import {
  CommonModule,
  NgClass,
  NgIf,
  NgSwitch,
  NgS<PERSON>Case,
  NgSwitchDefault,
  NgTemplateOutlet
} from "./chunk-AJ2TM24F.js";
import {
  ChangeDetectorRef,
  Component,
  ContentChild,
  Directive,
  ElementRef,
  HostBinding,
  Injectable,
  Input,
  NgModule,
  Optional,
  Renderer2,
  SkipSelf,
  TemplateRef,
  ViewChild,
  setClassMetadata,
  ɵɵInheritDefinitionFeature,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵariaProperty,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵpureFunction2,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-2AFYHOAX.js";

// node_modules/@carbon/icon-helpers/es/index.js
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;
  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }
  return target;
}
function _objectWithoutProperties(source, excluded) {
  if (source == null) return {};
  var target = _objectWithoutPropertiesLoose(source, excluded);
  var key, i;
  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0) continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
      target[key] = source[key];
    }
  }
  return target;
}
var _excluded = ["width", "height", "viewBox"];
var _excluded2 = ["tabindex"];
var defaultAttributes = {
  // Reference:
  // https://github.com/IBM/carbon-components-react/issues/1392
  // https://github.com/PolymerElements/iron-iconset-svg/pull/47
  // `focusable` is a string attribute which is why we do not use a boolean here
  focusable: "false",
  preserveAspectRatio: "xMidYMid meet"
};
function getAttributes() {
  var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, width = _ref.width, height = _ref.height, _ref$viewBox = _ref.viewBox, viewBox = _ref$viewBox === void 0 ? "0 0 ".concat(width, " ").concat(height) : _ref$viewBox, attributes = _objectWithoutProperties(_ref, _excluded);
  var tabindex = attributes.tabindex, rest = _objectWithoutProperties(attributes, _excluded2);
  var iconAttributes = _objectSpread2(_objectSpread2(_objectSpread2({}, defaultAttributes), rest), {}, {
    width,
    height,
    viewBox
  });
  if (iconAttributes["aria-label"] || iconAttributes["aria-labelledby"] || iconAttributes.title) {
    iconAttributes.role = "img";
    if (tabindex !== void 0 && tabindex !== null) {
      iconAttributes.focusable = "true";
      iconAttributes.tabindex = tabindex;
    }
  } else {
    iconAttributes["aria-hidden"] = true;
  }
  return iconAttributes;
}
function toString(descriptor) {
  var _descriptor$elem = descriptor.elem, elem = _descriptor$elem === void 0 ? "svg" : _descriptor$elem, _descriptor$attrs = descriptor.attrs, attrs = _descriptor$attrs === void 0 ? {} : _descriptor$attrs, _descriptor$content = descriptor.content, content = _descriptor$content === void 0 ? [] : _descriptor$content;
  var children = content.map(toString).join("");
  if (elem !== "svg") {
    return "<".concat(elem, " ").concat(formatAttributes(attrs), ">").concat(children, "</").concat(elem, ">");
  }
  return "<".concat(elem, " ").concat(formatAttributes(getAttributes(attrs)), ">").concat(children, "</").concat(elem, ">");
}
function formatAttributes(attrs) {
  return Object.keys(attrs).reduce(function(acc, key, index) {
    var attribute = "".concat(key, '="').concat(attrs[key], '"');
    if (index === 0) {
      return attribute;
    }
    return acc + " " + attribute;
  }, "");
}

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/add/16.js
var _16 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M17 15L17 8 15 8 15 15 8 15 8 17 15 17 15 24 17 24 17 17 24 17 24 15z"
    }
  }],
  "name": "add",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/add/20.js
var _20 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M17 15L17 8 15 8 15 15 8 15 8 17 15 17 15 24 17 24 17 17 24 17 24 15z"
    }
  }],
  "name": "add",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/bee/16.js
var _162 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M16 10a6 6 0 00-6 6v8a6 6 0 0012 0V16A6 6 0 0016 10zm-4.25 7.87h8.5v4.25h-8.5zM16 28.25A4.27 4.27 0 0111.75 24v-.13h8.5V24A4.27 4.27 0 0116 28.25zm4.25-12.13h-8.5V16a4.25 4.25 0 018.5 0zM30.66 19.21L24 13v9.1a4 4 0 008 0A3.83 3.83 0 0030.66 19.21zM28 24.35a2.25 2.25 0 01-2.25-2.25V17l3.72 3.47h0A2.05 2.05 0 0130.2 22 2.25 2.25 0 0128 24.35zM0 22.1a4 4 0 008 0V13L1.34 19.21A3.88 3.88 0 000 22.1zm2.48-1.56h0L6.25 17v5.1a2.25 2.25 0 01-4.5 0A2.05 2.05 0 012.48 20.54zM15 5.5A3.5 3.5 0 1011.5 9 3.5 3.5 0 0015 5.5zm-5.25 0A1.75 1.75 0 1111.5 7.25 1.77 1.77 0 019.75 5.5zM20.5 2A3.5 3.5 0 1024 5.5 3.5 3.5 0 0020.5 2zm0 5.25A1.75 1.75 0 1122.25 5.5 1.77 1.77 0 0120.5 7.25z"
    }
  }],
  "name": "bee",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/bee/20.js
var _202 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M16 10a6 6 0 00-6 6v8a6 6 0 0012 0V16A6 6 0 0016 10zm-4.25 7.87h8.5v4.25h-8.5zM16 28.25A4.27 4.27 0 0111.75 24v-.13h8.5V24A4.27 4.27 0 0116 28.25zm4.25-12.13h-8.5V16a4.25 4.25 0 018.5 0zM30.66 19.21L24 13v9.1a4 4 0 008 0A3.83 3.83 0 0030.66 19.21zM28 24.35a2.25 2.25 0 01-2.25-2.25V17l3.72 3.47h0A2.05 2.05 0 0130.2 22 2.25 2.25 0 0128 24.35zM0 22.1a4 4 0 008 0V13L1.34 19.21A3.88 3.88 0 000 22.1zm2.48-1.56h0L6.25 17v5.1a2.25 2.25 0 01-4.5 0A2.05 2.05 0 012.48 20.54zM15 5.5A3.5 3.5 0 1011.5 9 3.5 3.5 0 0015 5.5zm-5.25 0A1.75 1.75 0 1111.5 7.25 1.77 1.77 0 019.75 5.5zM20.5 2A3.5 3.5 0 1024 5.5 3.5 3.5 0 0020.5 2zm0 5.25A1.75 1.75 0 1122.25 5.5 1.77 1.77 0 0120.5 7.25z"
    }
  }],
  "name": "bee",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/calendar/16.js
var _163 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M26,4h-4V2h-2v2h-8V2h-2v2H6C4.9,4,4,4.9,4,6v20c0,1.1,0.9,2,2,2h20c1.1,0,2-0.9,2-2V6C28,4.9,27.1,4,26,4z M26,26H6V12h20	V26z M26,10H6V6h4v2h2V6h8v2h2V6h4V10z"
    }
  }],
  "name": "calendar",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/carbon/16.js
var _164 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M13.5,30.8149a1.0011,1.0011,0,0,1-.4927-.13l-8.5-4.815A1,1,0,0,1,4,25V15a1,1,0,0,1,.5073-.87l8.5-4.815a1.0013,1.0013,0,0,1,.9854,0l8.5,4.815A1,1,0,0,1,23,15V25a1,1,0,0,1-.5073.87l-8.5,4.815A1.0011,1.0011,0,0,1,13.5,30.8149ZM6,24.417l7.5,4.2485L21,24.417V15.583l-7.5-4.2485L6,15.583Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M28,17H26V7.583L18.5,3.3345,10.4927,7.87,9.5073,6.13l8.5-4.815a1.0013,1.0013,0,0,1,.9854,0l8.5,4.815A1,1,0,0,1,28,7Z"
    }
  }],
  "name": "carbon",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/carbon/20.js
var _203 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M13.5,30.8149a1.0011,1.0011,0,0,1-.4927-.13l-8.5-4.815A1,1,0,0,1,4,25V15a1,1,0,0,1,.5073-.87l8.5-4.815a1.0013,1.0013,0,0,1,.9854,0l8.5,4.815A1,1,0,0,1,23,15V25a1,1,0,0,1-.5073.87l-8.5,4.815A1.0011,1.0011,0,0,1,13.5,30.8149ZM6,24.417l7.5,4.2485L21,24.417V15.583l-7.5-4.2485L6,15.583Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M28,17H26V7.583L18.5,3.3345,10.4927,7.87,9.5073,6.13l8.5-4.815a1.0013,1.0013,0,0,1,.9854,0l8.5,4.815A1,1,0,0,1,28,7Z"
    }
  }],
  "name": "carbon",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/caret--down/16.js
var _165 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M24 12L16 22 8 12z"
    }
  }],
  "name": "caret--down",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/caret--left/16.js
var _166 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M20 24L10 16 20 8z"
    }
  }],
  "name": "caret--left",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/caret--right/16.js
var _167 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M12 8L22 16 12 24z"
    }
  }],
  "name": "caret--right",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/caret--up/16.js
var _168 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8 20L16 10 24 20z"
    }
  }],
  "name": "caret--up",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/checkmark/16.js
var _169 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M13 24L4 15 5.414 13.586 13 21.171 26.586 7.586 28 9 13 24z"
    }
  }],
  "name": "checkmark",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/checkmark--filled/16.js
var _1610 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8,1C4.1,1,1,4.1,1,8c0,3.9,3.1,7,7,7s7-3.1,7-7C15,4.1,11.9,1,8,1z M7,11L4.3,8.3l0.9-0.8L7,9.3l4-3.9l0.9,0.8L7,11z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M7,11L4.3,8.3l0.9-0.8L7,9.3l4-3.9l0.9,0.8L7,11z",
      "data-icon-path": "inner-path",
      "opacity": "0"
    }
  }],
  "name": "checkmark--filled",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/checkmark--filled/20.js
var _204 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 20 20",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M10,1c-4.9,0-9,4.1-9,9s4.1,9,9,9s9-4,9-9S15,1,10,1z M8.7,13.5l-3.2-3.2l1-1l2.2,2.2l4.8-4.8l1,1L8.7,13.5z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M8.7,13.5l-3.2-3.2l1-1l2.2,2.2l4.8-4.8l1,1L8.7,13.5z",
      "data-icon-path": "inner-path",
      "opacity": "0"
    }
  }],
  "name": "checkmark--filled",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/checkmark--outline/16.js
var _1611 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M14 21.414L9 16.413 10.413 15 14 18.586 21.585 11 23 12.415 14 21.414z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,26A12,12,0,1,1,28,16,12,12,0,0,1,16,28Z"
    }
  }],
  "name": "checkmark--outline",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/checkbox/16.js
var _1612 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM6,26V6H26V26Z"
    }
  }],
  "name": "checkbox",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/checkbox--checked--filled/16.js
var _1613 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM14,21.5,9,16.5427,10.5908,15,14,18.3456,21.4087,11l1.5918,1.5772Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M14,21.5,9,16.5427,10.5908,15,14,18.3456,21.4087,11l1.5918,1.5772Z",
      "data-icon-path": "inner-path"
    }
  }],
  "name": "checkbox--checked--filled",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/chevron--down/16.js
var _1614 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8 11L3 6 3.7 5.3 8 9.6 12.3 5.3 13 6z"
    }
  }],
  "name": "chevron--down",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/chevron--right/16.js
var _1615 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M11 8L6 13 5.3 12.3 9.6 8 5.3 3.7 6 3z"
    }
  }],
  "name": "chevron--right",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/circle-dash/16.js
var _1616 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M7.7 4.7a14.7 14.7 0 00-3 3.1L6.3 9A13.26 13.26 0 018.9 6.3zM4.6 12.3l-1.9-.6A12.51 12.51 0 002 16H4A11.48 11.48 0 014.6 12.3zM2.7 20.4a14.4 14.4 0 002 3.9l1.6-1.2a12.89 12.89 0 01-1.7-3.3zM7.8 27.3a14.4 14.4 0 003.9 2l.6-1.9A12.89 12.89 0 019 25.7zM11.7 2.7l.6 1.9A11.48 11.48 0 0116 4V2A12.51 12.51 0 0011.7 2.7zM24.2 27.3a15.18 15.18 0 003.1-3.1L25.7 23A11.53 11.53 0 0123 25.7zM27.4 19.7l1.9.6A15.47 15.47 0 0030 16H28A11.48 11.48 0 0127.4 19.7zM29.2 11.6a14.4 14.4 0 00-2-3.9L25.6 8.9a12.89 12.89 0 011.7 3.3zM24.1 4.6a14.4 14.4 0 00-3.9-2l-.6 1.9a12.89 12.89 0 013.3 1.7zM20.3 29.3l-.6-1.9A11.48 11.48 0 0116 28v2A21.42 21.42 0 0020.3 29.3z"
    }
  }],
  "name": "circle-dash",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/close/16.js
var _1617 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M17.4141 16L24 9.4141 22.5859 8 16 14.5859 9.4143 8 8 9.4141 14.5859 16 8 22.5859 9.4143 24 16 17.4141 22.5859 24 24 22.5859 17.4141 16z"
    }
  }],
  "name": "close",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/close/20.js
var _205 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M17.4141 16L24 9.4141 22.5859 8 16 14.5859 9.4143 8 8 9.4141 14.5859 16 8 22.5859 9.4143 24 16 17.4141 22.5859 24 24 22.5859 17.4141 16z"
    }
  }],
  "name": "close",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/copy/16.js
var _1618 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"
    }
  }],
  "name": "copy",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/copy/20.js
var _206 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"
    }
  }],
  "name": "copy",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/data--2/16.js
var _1619 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M4 6H15V8H4zM18 6H28V8H18zM21 12H28V14H21zM11 12H18V14H11zM4 12H8V14H4zM4 18H28V20H4zM4 24H21V26H4zM24 24H28V26H24z"
    }
  }],
  "name": "data--2",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/data--2/20.js
var _207 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M4 6H15V8H4zM18 6H28V8H18zM21 12H28V14H21zM11 12H18V14H11zM4 12H8V14H4zM4 18H28V20H4zM4 24H21V26H4zM24 24H28V26H24z"
    }
  }],
  "name": "data--2",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/document/16.js
var _1620 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M25.7,9.3l-7-7C18.5,2.1,18.3,2,18,2H8C6.9,2,6,2.9,6,4v24c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V10C26,9.7,25.9,9.5,25.7,9.3	z M18,4.4l5.6,5.6H18V4.4z M24,28H8V4h8v6c0,1.1,0.9,2,2,2h6V28z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M10 22H22V24H10zM10 16H22V18H10z"
    }
  }],
  "name": "document",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/document/20.js
var _208 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M25.7,9.3l-7-7C18.5,2.1,18.3,2,18,2H8C6.9,2,6,2.9,6,4v24c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V10C26,9.7,25.9,9.5,25.7,9.3	z M18,4.4l5.6,5.6H18V4.4z M24,28H8V4h8v6c0,1.1,0.9,2,2,2h6V28z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M10 22H22V24H10zM10 16H22V18H10z"
    }
  }],
  "name": "document",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/download/16.js
var _1621 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M13 7L12.3 6.3 8.5 10.1 8.5 1 7.5 1 7.5 10.1 3.7 6.3 3 7 8 12zM13 12v2H3v-2H2v2l0 0c0 .6.4 1 1 1h10c.6 0 1-.4 1-1l0 0v-2H13z"
    }
  }],
  "name": "download",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/error--filled/16.js
var _1622 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8,1C4.1,1,1,4.1,1,8s3.1,7,7,7s7-3.1,7-7S11.9,1,8,1z M10.7,11.5L4.5,5.3l0.8-0.8l6.2,6.2L10.7,11.5z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M10.7,11.5L4.5,5.3l0.8-0.8l6.2,6.2L10.7,11.5z",
      "data-icon-path": "inner-path",
      "opacity": "0"
    }
  }],
  "name": "error--filled",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/error--filled/20.js
var _209 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 20 20",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M10,1c-5,0-9,4-9,9s4,9,9,9s9-4,9-9S15,1,10,1z M13.5,14.5l-8-8l1-1l8,8L13.5,14.5z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M13.5,14.5l-8-8l1-1l8,8L13.5,14.5z",
      "data-icon-path": "inner-path",
      "opacity": "0"
    }
  }],
  "name": "error--filled",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/fade/16.js
var _1623 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M4.1 12.6l-.6.8c.6.5 1.3.9 2.1 1.2l.3-.9C5.3 13.4 4.7 13 4.1 12.6zM2.1 9l-1 .2c.1.8.4 1.6.8 2.3L2.8 11C2.4 10.4 2.2 9.7 2.1 9zM5.9 2.4L5.6 1.4C4.8 1.7 4.1 2.1 3.5 2.7l.6.8C4.7 3 5.3 2.6 5.9 2.4zM2.8 5L1.9 4.5C1.5 5.2 1.3 6 1.1 6.8l1 .2C2.2 6.3 2.5 5.6 2.8 5zM8 1v1c3.3 0 6 2.7 6 6s-2.7 6-6 6v1c3.9 0 7-3.1 7-7S11.9 1 8 1z"
    }
  }],
  "name": "fade",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/fade/20.js
var _2010 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8.24 25.14L7 26.67a13.79 13.79 0 004.18 2.44l.69-1.87A12 12 0 018.24 25.14zM4.19 18l-2 .41A14.09 14.09 0 003.86 23L5.59 22A12.44 12.44 0 014.19 18zM11.82 4.76l-.69-1.87A13.79 13.79 0 007 5.33L8.24 6.86A12 12 0 0111.82 4.76zM5.59 10L3.86 9a14.37 14.37 0 00-1.64 4.59l2 .34A12.05 12.05 0 015.59 10zM16 2V4a12 12 0 010 24v2A14 14 0 0016 2z"
    }
  }],
  "name": "fade",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/folder/16.js
var _1624 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M11.17,6l3.42,3.41.58.59H28V26H4V6h7.17m0-2H4A2,2,0,0,0,2,6V26a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2H16L12.59,4.59A2,2,0,0,0,11.17,4Z"
    }
  }],
  "name": "folder",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/incomplete/16.js
var _1625 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M23.7642 6.8593l1.2851-1.5315A13.976 13.976 0 0020.8672 2.887l-.6836 1.8776A11.9729 11.9729 0 0123.7642 6.8593zM27.81 14l1.9677-.4128A13.8888 13.8888 0 0028.14 9.0457L26.4087 10A12.52 12.52 0 0127.81 14zM20.1836 27.2354l.6836 1.8776a13.976 13.976 0 004.1821-2.4408l-1.2851-1.5315A11.9729 11.9729 0 0120.1836 27.2354zM26.4087 22L28.14 23a14.14 14.14 0 001.6382-4.5872L27.81 18.0659A12.1519 12.1519 0 0126.4087 22zM16 30V2a14 14 0 000 28z"
    }
  }],
  "name": "incomplete",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/information--filled/16.js
var _1626 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M16,8a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,13.875H17.125v-8H13v2.25h1.875v5.75H12v2.25h8Z",
      "data-icon-path": "inner-path"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,6a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,16.125H12v-2.25h2.875v-5.75H13v-2.25h4.125v8H20Z"
    }
  }],
  "name": "information--filled",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/information--filled/20.js
var _2011 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M16,8a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,13.875H17.125v-8H13v2.25h1.875v5.75H12v2.25h8Z",
      "data-icon-path": "inner-path"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,6a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,16.125H12v-2.25h2.875v-5.75H13v-2.25h4.125v8H20Z"
    }
  }],
  "name": "information--filled",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/information--square--filled/20.js
var _2012 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M16,8a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,13.875H17.125v-8H13v2.25h1.875v5.75H12v2.25h8Z",
      "data-icon-path": "inner-path"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M26,4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2H26a2,2,0,0,0,2-2V6A2,2,0,0,0,26,4ZM16,8a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,16.125H12v-2.25h2.875v-5.75H13v-2.25h4.125v8H20Z"
    }
  }],
  "name": "information--square--filled",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/menu/16.js
var _1627 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M2 12H14V13H2zM2 9H14V10H2zM2 6H14V7H2zM2 3H14V4H2z"
    }
  }],
  "name": "menu",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/menu/20.js
var _2013 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 20 20",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M2 14.8H18V16H2zM2 11.2H18V12.399999999999999H2zM2 7.6H18V8.799999999999999H2zM2 4H18V5.2H2z"
    }
  }],
  "name": "menu",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/overflow-menu--vertical/16.js
var _1628 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "circle",
    "attrs": {
      "cx": "16",
      "cy": "8",
      "r": "2"
    }
  }, {
    "elem": "circle",
    "attrs": {
      "cx": "16",
      "cy": "16",
      "r": "2"
    }
  }, {
    "elem": "circle",
    "attrs": {
      "cx": "16",
      "cy": "24",
      "r": "2"
    }
  }],
  "name": "overflow-menu--vertical",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/overflow-menu--horizontal/16.js
var _1629 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "circle",
    "attrs": {
      "cx": "8",
      "cy": "16",
      "r": "2"
    }
  }, {
    "elem": "circle",
    "attrs": {
      "cx": "16",
      "cy": "16",
      "r": "2"
    }
  }, {
    "elem": "circle",
    "attrs": {
      "cx": "24",
      "cy": "16",
      "r": "2"
    }
  }],
  "name": "overflow-menu--horizontal",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/save/16.js
var _1630 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M13.9,4.6l-2.5-2.5C11.3,2.1,11.1,2,11,2H3C2.4,2,2,2.4,2,3v10c0,0.6,0.4,1,1,1h10c0.6,0,1-0.4,1-1V5	C14,4.9,13.9,4.7,13.9,4.6z M6,3h4v2H6V3z M10,13H6V9h4V13z M11,13V9c0-0.6-0.4-1-1-1H6C5.4,8,5,8.4,5,9v4H3V3h2v2c0,0.6,0.4,1,1,1	h4c0.6,0,1-0.4,1-1V3.2l2,2V13H11z"
    }
  }],
  "name": "save",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/search/16.js
var _1631 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M15,14.3L10.7,10c1.9-2.3,1.6-5.8-0.7-7.7S4.2,0.7,2.3,3S0.7,8.8,3,10.7c2,1.7,5,1.7,7,0l4.3,4.3L15,14.3z M2,6.5	C2,4,4,2,6.5,2S11,4,11,6.5S9,11,6.5,11S2,9,2,6.5z"
    }
  }],
  "name": "search",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/settings/16.js
var _1632 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M13.5,8.4c0-0.1,0-0.3,0-0.4c0-0.1,0-0.3,0-0.4l1-0.8c0.4-0.3,0.4-0.9,0.2-1.3l-1.2-2C13.3,3.2,13,3,12.6,3	c-0.1,0-0.2,0-0.3,0.1l-1.2,0.4c-0.2-0.1-0.4-0.3-0.7-0.4l-0.3-1.3C10.1,1.3,9.7,1,9.2,1H6.8c-0.5,0-0.9,0.3-1,0.8L5.6,3.1	C5.3,3.2,5.1,3.3,4.9,3.4L3.7,3C3.6,3,3.5,3,3.4,3C3,3,2.7,3.2,2.5,3.5l-1.2,2C1.1,5.9,1.2,6.4,1.6,6.8l0.9,0.9c0,0.1,0,0.3,0,0.4	c0,0.1,0,0.3,0,0.4L1.6,9.2c-0.4,0.3-0.5,0.9-0.2,1.3l1.2,2C2.7,12.8,3,13,3.4,13c0.1,0,0.2,0,0.3-0.1l1.2-0.4	c0.2,0.1,0.4,0.3,0.7,0.4l0.3,1.3c0.1,0.5,0.5,0.8,1,0.8h2.4c0.5,0,0.9-0.3,1-0.8l0.3-1.3c0.2-0.1,0.4-0.2,0.7-0.4l1.2,0.4	c0.1,0,0.2,0.1,0.3,0.1c0.4,0,0.7-0.2,0.9-0.5l1.1-2c0.2-0.4,0.2-0.9-0.2-1.3L13.5,8.4z M12.6,12l-1.7-0.6c-0.4,0.3-0.9,0.6-1.4,0.8	L9.2,14H6.8l-0.4-1.8c-0.5-0.2-0.9-0.5-1.4-0.8L3.4,12l-1.2-2l1.4-1.2c-0.1-0.5-0.1-1.1,0-1.6L2.2,6l1.2-2l1.7,0.6	C5.5,4.2,6,4,6.5,3.8L6.8,2h2.4l0.4,1.8c0.5,0.2,0.9,0.5,1.4,0.8L12.6,4l1.2,2l-1.4,1.2c0.1,0.5,0.1,1.1,0,1.6l1.4,1.2L12.6,12z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M8,11c-1.7,0-3-1.3-3-3s1.3-3,3-3s3,1.3,3,3C11,9.6,9.7,11,8,11C8,11,8,11,8,11z M8,6C6.9,6,6,6.8,6,7.9C6,7.9,6,8,6,8	c0,1.1,0.8,2,1.9,2c0,0,0.1,0,0.1,0c1.1,0,2-0.8,2-1.9c0,0,0-0.1,0-0.1C10,6.9,9.2,6,8,6C8.1,6,8,6,8,6z"
    }
  }],
  "name": "settings",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/settings--adjust/16.js
var _1633 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M30 8h-4.1c-.5-2.3-2.5-4-4.9-4s-4.4 1.7-4.9 4H2v2h14.1c.5 2.3 2.5 4 4.9 4s4.4-1.7 4.9-4H30V8zM21 12c-1.7 0-3-1.3-3-3s1.3-3 3-3 3 1.3 3 3S22.7 12 21 12zM2 24h4.1c.5 2.3 2.5 4 4.9 4s4.4-1.7 4.9-4H30v-2H15.9c-.5-2.3-2.5-4-4.9-4s-4.4 1.7-4.9 4H2V24zM11 20c1.7 0 3 1.3 3 3s-1.3 3-3 3-3-1.3-3-3S9.3 20 11 20z"
    }
  }],
  "name": "settings--adjust",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/subtract/16.js
var _1634 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8 15H24V17H8z"
    }
  }],
  "name": "subtract",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/trash-can/16.js
var _1635 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M12 12H14V24H12zM18 12H20V24H18z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M4 6V8H6V28a2 2 0 002 2H24a2 2 0 002-2V8h2V6zM8 28V8H24V28zM12 2H20V4H12z"
    }
  }],
  "name": "trash-can",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/warning/16.js
var _1636 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8,1C4.1,1,1,4.1,1,8s3.1,7,7,7s7-3.1,7-7S11.9,1,8,1z M8,14c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S11.3,14,8,14z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M7.5 4H8.5V9H7.5zM8 10.2c-.4 0-.8.3-.8.8s.3.8.8.8c.4 0 .8-.3.8-.8S8.4 10.2 8 10.2z"
    }
  }],
  "name": "warning",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/warning--filled/16.js
var _1637 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M8,1C4.2,1,1,4.2,1,8s3.2,7,7,7s7-3.1,7-7S11.9,1,8,1z M7.5,4h1v5h-1C7.5,9,7.5,4,7.5,4z M8,12.2	c-0.4,0-0.8-0.4-0.8-0.8s0.3-0.8,0.8-0.8c0.4,0,0.8,0.4,0.8,0.8S8.4,12.2,8,12.2z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M7.5,4h1v5h-1C7.5,9,7.5,4,7.5,4z M8,12.2c-0.4,0-0.8-0.4-0.8-0.8s0.3-0.8,0.8-0.8	c0.4,0,0.8,0.4,0.8,0.8S8.4,12.2,8,12.2z",
      "data-icon-path": "inner-path",
      "opacity": "0"
    }
  }],
  "name": "warning--filled",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/warning--filled/20.js
var _2014 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 20 20",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M10,1c-5,0-9,4-9,9s4,9,9,9s9-4,9-9S15,1,10,1z M9.2,5h1.5v7H9.2V5z M10,16c-0.6,0-1-0.4-1-1s0.4-1,1-1	s1,0.4,1,1S10.6,16,10,16z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M9.2,5h1.5v7H9.2V5z M10,16c-0.6,0-1-0.4-1-1s0.4-1,1-1s1,0.4,1,1S10.6,16,10,16z",
      "data-icon-path": "inner-path",
      "opacity": "0"
    }
  }],
  "name": "warning--filled",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/warning--alt--filled/16.js
var _1638 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M16,26a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16,26Zm-1.125-5h2.25V12h-2.25Z",
      "data-icon-path": "inner-path"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M16.002,6.1714h-.004L4.6487,27.9966,4.6506,28H27.3494l.0019-.0034ZM14.875,12h2.25v9h-2.25ZM16,26a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16,26Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M29,30H3a1,1,0,0,1-.8872-1.4614l13-25a1,1,0,0,1,1.7744,0l13,25A1,1,0,0,1,29,30ZM4.6507,28H27.3493l.002-.0033L16.002,6.1714h-.004L4.6487,27.9967Z"
    }
  }],
  "name": "warning--alt--filled",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/warning--alt--filled/20.js
var _2015 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 32 32",
    "fill": "currentColor",
    "width": 20,
    "height": 20
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "fill": "none",
      "d": "M16,26a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16,26Zm-1.125-5h2.25V12h-2.25Z",
      "data-icon-path": "inner-path"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M16.002,6.1714h-.004L4.6487,27.9966,4.6506,28H27.3494l.0019-.0034ZM14.875,12h2.25v9h-2.25ZM16,26a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16,26Z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M29,30H3a1,1,0,0,1-.8872-1.4614l13-25a1,1,0,0,1,1.7744,0l13,25A1,1,0,0,1,29,30ZM4.6507,28H27.3493l.002-.0033L16.002,6.1714h-.004L4.6487,27.9967Z"
    }
  }],
  "name": "warning--alt--filled",
  "size": 20
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/view/16.js
var _1639 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M15.5,7.8C14.3,4.7,11.3,2.6,8,2.5C4.7,2.6,1.7,4.7,0.5,7.8c0,0.1,0,0.2,0,0.3c1.2,3.1,4.1,5.2,7.5,5.3	c3.3-0.1,6.3-2.2,7.5-5.3C15.5,8.1,15.5,7.9,15.5,7.8z M8,12.5c-2.7,0-5.4-2-6.5-4.5c1-2.5,3.8-4.5,6.5-4.5s5.4,2,6.5,4.5	C13.4,10.5,10.6,12.5,8,12.5z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M8,5C6.3,5,5,6.3,5,8s1.3,3,3,3s3-1.3,3-3S9.7,5,8,5z M8,10c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S9.1,10,8,10z"
    }
  }],
  "name": "view",
  "size": 16
};

// node_modules/carbon-components-angular/node_modules/@carbon/icons/es/view--off/16.js
var _1640 = {
  "elem": "svg",
  "attrs": {
    "xmlns": "http://www.w3.org/2000/svg",
    "viewBox": "0 0 16 16",
    "fill": "currentColor",
    "width": 16,
    "height": 16
  },
  "content": [{
    "elem": "path",
    "attrs": {
      "d": "M2.6,11.3l0.7-0.7C2.6,9.8,1.9,9,1.5,8c1-2.5,3.8-4.5,6.5-4.5c0.7,0,1.4,0.1,2,0.4l0.8-0.8C9.9,2.7,9,2.5,8,2.5	C4.7,2.6,1.7,4.7,0.5,7.8c0,0.1,0,0.2,0,0.3C1,9.3,1.7,10.4,2.6,11.3z"
    }
  }, {
    "elem": "path",
    "attrs": {
      "d": "M6 7.9c.1-1 .9-1.8 1.8-1.8l.9-.9C7.2 4.7 5.5 5.6 5.1 7.2 5 7.7 5 8.3 5.1 8.8L6 7.9zM15.5 7.8c-.6-1.5-1.6-2.8-2.9-3.7L15 1.7 14.3 1 1 14.3 1.7 15l2.6-2.6c1.1.7 2.4 1 3.7 1.1 3.3-.1 6.3-2.2 7.5-5.3C15.5 8.1 15.5 7.9 15.5 7.8zM10 8c0 1.1-.9 2-2 2-.3 0-.7-.1-1-.3L9.7 7C9.9 7.3 10 7.6 10 8zM8 12.5c-1 0-2.1-.3-3-.8l1.3-1.3c1.4.9 3.2.6 4.2-.8.7-1 .7-2.4 0-3.4l1.4-1.4c1.1.8 2 1.9 2.6 3.2C13.4 10.5 10.6 12.5 8 12.5z"
    }
  }],
  "name": "view--off",
  "size": 16
};

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-icon.mjs
var IconCache = class {
};
var IconNameNotFoundError = class extends Error {
  constructor(name) {
    super(`Icon ${name} not found`);
  }
};
var IconSizeNotFoundError = class extends Error {
  constructor(size, name) {
    super(`Size ${size} for ${name} not found`);
  }
};
var IconMemoryCache = class extends IconCache {
  constructor() {
    super(...arguments);
    this.iconMap = /* @__PURE__ */ new Map();
  }
  get(name, size) {
    if (!this.iconMap.has(name)) {
      throw new IconNameNotFoundError(name);
    }
    const sizeMap = this.iconMap.get(name);
    if (!sizeMap.has(size)) {
      throw new IconSizeNotFoundError(size, name);
    }
    return sizeMap.get(size);
  }
  set(name, size, descriptor) {
    if (!this.iconMap.has(name)) {
      this.iconMap.set(name, /* @__PURE__ */ new Map());
    }
    const sizeMap = this.iconMap.get(name);
    sizeMap.set(size, descriptor);
  }
};
var IconService = class {
  constructor() {
    this.iconCache = new IconMemoryCache();
  }
  /**
   * Registers an array of icons based on the metadata provided by `@carbon/icons`
   */
  registerAll(descriptors) {
    descriptors.forEach((icon) => this.register(icon));
  }
  /**
   * Registers an icon based on the metadata provided by `@carbon/icons`
   */
  register(descriptor) {
    const {
      name
    } = descriptor;
    this.registerAs(name, descriptor);
  }
  /**
   * Registers an icon based on a uniqe name and metadata provided by `@carbon/icons`
   */
  registerAs(name, descriptor) {
    const {
      size
    } = descriptor;
    this.iconCache.set(name, size.toString(), descriptor);
  }
  /**
   * Gets an icon, converts it to a string, and caches the result
   */
  get(name, size) {
    try {
      const icon = this.iconCache.get(name, size.toString());
      if (!icon.svg) {
        icon.svg = toString(icon);
      }
      return icon;
    } catch (e) {
      throw e;
    }
  }
  /**
   * Configure various service settings (caching strategy ...)
   */
  configure(options) {
    this.iconCache = options.cache;
  }
};
IconService.ɵfac = function IconService_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || IconService)();
};
IconService.ɵprov = ɵɵdefineInjectable({
  token: IconService,
  factory: IconService.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IconService, [{
    type: Injectable
  }], null, null);
})();
var IconDirective = class _IconDirective {
  constructor(elementRef, iconService) {
    this.elementRef = elementRef;
    this.iconService = iconService;
    this.cdsIcon = "";
    this.size = "16";
    this.title = "";
    this.ariaLabel = "";
    this.ariaLabelledBy = "";
    this.ariaHidden = "";
    this.isFocusable = false;
  }
  /**
   * @deprecated since v5 - Use `cdsIcon` input property instead
   */
  set ibmIcon(iconName) {
    this.cdsIcon = iconName;
  }
  renderIcon(iconName) {
    const root = this.elementRef.nativeElement;
    let icon;
    try {
      icon = this.iconService.get(iconName, this.size.toString());
    } catch (error) {
      console.warn(error);
      return;
    }
    const domParser = new DOMParser();
    const rawSVG = icon.svg;
    const svgElement = domParser.parseFromString(rawSVG, "image/svg+xml").documentElement;
    let node = root.tagName.toUpperCase() !== "SVG" ? svgElement : svgElement.firstChild;
    root.innerHTML = "";
    while (node) {
      root.appendChild(root.ownerDocument.importNode(node, true));
      node = node.nextSibling;
    }
    const svg = root.tagName.toUpperCase() !== "SVG" ? svgElement : root;
    const xmlns = "http://www.w3.org/2000/svg";
    svg.setAttribute("xmlns", xmlns);
    const attributes = getAttributes({
      width: icon.attrs.width,
      height: icon.attrs.height,
      viewBox: icon.attrs.viewBox,
      title: this.title,
      "aria-label": this.ariaLabel,
      "aria-labelledby": this.ariaLabelledBy,
      "aria-hidden": this.ariaHidden,
      focusable: this.isFocusable.toString(),
      fill: icon.attrs.fill
    });
    const attrKeys = Object.keys(attributes);
    for (let i = 0; i < attrKeys.length; i++) {
      const key = attrKeys[i];
      const value = attributes[key];
      if (key === "title") {
        continue;
      }
      if (value) {
        svg.setAttribute(key, value);
      }
    }
    if (attributes["title"]) {
      const title = document.createElementNS(xmlns, "title");
      title.textContent = attributes.title;
      _IconDirective.titleIdCounter++;
      title.setAttribute("id", `${icon.name}-title-${_IconDirective.titleIdCounter}`);
      svg.insertBefore(title, svg.firstElementChild);
      svg.setAttribute("aria-labelledby", `${icon.name}-title-${_IconDirective.titleIdCounter}`);
    }
  }
  ngAfterViewInit() {
    this.renderIcon(this.cdsIcon);
  }
  ngOnChanges({
    cdsIcon
  }) {
    if (cdsIcon && !cdsIcon.isFirstChange()) {
      this.renderIcon(this.cdsIcon);
    }
  }
};
IconDirective.titleIdCounter = 0;
IconDirective.ɵfac = function IconDirective_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || IconDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(IconService));
};
IconDirective.ɵdir = ɵɵdefineDirective({
  type: IconDirective,
  selectors: [["", "cdsIcon", ""], ["", "ibmIcon", ""]],
  inputs: {
    ibmIcon: "ibmIcon",
    cdsIcon: "cdsIcon",
    size: "size",
    title: "title",
    ariaLabel: "ariaLabel",
    ariaLabelledBy: "ariaLabelledBy",
    ariaHidden: "ariaHidden",
    isFocusable: "isFocusable"
  },
  standalone: false,
  features: [ɵɵNgOnChangesFeature]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IconDirective, [{
    type: Directive,
    args: [{
      selector: "[cdsIcon], [ibmIcon]"
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: IconService
    }];
  }, {
    ibmIcon: [{
      type: Input
    }],
    cdsIcon: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    title: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    ariaLabelledBy: [{
      type: Input
    }],
    ariaHidden: [{
      type: Input
    }],
    isFocusable: [{
      type: Input
    }]
  });
})();
function ICON_SERVICE_PROVIDER_FACTORY(parentService) {
  return parentService || new IconService();
}
var ICON_SERVICE_PROVIDER = {
  provide: IconService,
  deps: [[new Optional(), new SkipSelf(), IconService]],
  useFactory: ICON_SERVICE_PROVIDER_FACTORY
};
var IconModule = class {
  constructor(iconService) {
    this.iconService = iconService;
    iconService.registerAll([_16, _20, _162, _202, _163, _164, _203, _165, _166, _167, _168, _169, _1610, _204, _1611, _1612, _1613, _1614, _1615, _1616, _1617, _205, _1618, _206, _1619, _207, _1620, _208, _1621, _1622, _209, _1623, _2010, _1624, _1625, _1626, _2011, _2012, _1627, _2013, _1628, _1629, _1630, _1631, _1632, _1633, _1634, _1635, _1639, _1640, _1636, _1637, _2014, _1638, _2015]);
  }
};
IconModule.ɵfac = function IconModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || IconModule)(ɵɵinject(IconService));
};
IconModule.ɵmod = ɵɵdefineNgModule({
  type: IconModule,
  declarations: [IconDirective],
  imports: [CommonModule],
  exports: [IconDirective]
});
IconModule.ɵinj = ɵɵdefineInjector({
  providers: [ICON_SERVICE_PROVIDER],
  imports: [CommonModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IconModule, [{
    type: NgModule,
    args: [{
      declarations: [IconDirective],
      exports: [IconDirective],
      imports: [CommonModule],
      providers: [ICON_SERVICE_PROVIDER]
    }]
  }], function() {
    return [{
      type: IconService
    }];
  }, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-input.mjs
var _c0 = ["wrapper"];
var _c1 = ["*", [["", "cdsTextArea", ""], ["", "ibmTextArea", ""], ["textarea"]]];
var _c2 = ["*", "[cdsTextArea],[ibmTextArea],textarea"];
var _c3 = (a0) => ({
  "cds--label--disabled": a0
});
var _c4 = (a0) => ({
  "cds--text-area__wrapper--warn": a0
});
var _c5 = (a0) => ({
  "cds--form__helper-text--disabled": a0
});
function TextareaLabelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "span", 4)(2, "div", 5);
    ɵɵelementContainerEnd();
  }
}
function TextareaLabelComponent_ng_container_1_3_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_3_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.labelTemplate);
  }
}
function TextareaLabelComponent_ng_container_1_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function TextareaLabelComponent_ng_container_1__svg_svg_8_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 13);
  }
}
function TextareaLabelComponent_ng_container_1__svg_svg_9_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 14);
  }
}
function TextareaLabelComponent_ng_container_1_10_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_10_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_10_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.textAreaTemplate);
  }
}
function TextareaLabelComponent_ng_container_1_ng_template_11_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0, 1);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 17);
    ɵɵtemplate(1, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_2_Template, 1, 1, null, 3);
    ɵɵnamespaceSVG();
    ɵɵelement(3, "svg", 13);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_div_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 17);
    ɵɵtemplate(1, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_2_Template, 1, 1, null, 3);
    ɵɵnamespaceSVG();
    ɵɵelement(3, "svg", 14);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_13_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "hr", 15);
    ɵɵtemplate(2, TextareaLabelComponent_ng_container_1_ng_container_13_div_2_Template, 4, 2, "div", 16)(3, TextareaLabelComponent_ng_container_1_ng_container_13_div_3_Template, 4, 2, "div", 16);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.helperText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 19);
    ɵɵtemplate(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngClass", ɵɵpureFunction1(3, _c5, ctx_r0.disabled));
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 17);
    ɵɵtemplate(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 12);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_div_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 17);
    ɵɵtemplate(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextareaLabelComponent_ng_container_1_ng_container_14_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, TextareaLabelComponent_ng_container_1_ng_container_14_div_1_Template, 3, 5, "div", 18)(2, TextareaLabelComponent_ng_container_1_ng_container_14_div_2_Template, 3, 2, "div", 16)(3, TextareaLabelComponent_ng_container_1_ng_container_14_div_3_Template, 3, 2, "div", 16);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextareaLabelComponent_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelementStart(1, "div", 6)(2, "label", 7);
    ɵɵtemplate(3, TextareaLabelComponent_ng_container_1_3_Template, 1, 1, null, 8)(4, TextareaLabelComponent_ng_container_1_ng_template_4_Template, 1, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor);
    ɵɵelementEnd()();
    ɵɵelementStart(6, "div", 9, 1);
    ɵɵtemplate(8, TextareaLabelComponent_ng_container_1__svg_svg_8_Template, 1, 0, "svg", 10)(9, TextareaLabelComponent_ng_container_1__svg_svg_9_Template, 1, 0, "svg", 11)(10, TextareaLabelComponent_ng_container_1_10_Template, 1, 1, null, 8)(11, TextareaLabelComponent_ng_container_1_ng_template_11_Template, 1, 0, "ng-template", null, 2, ɵɵtemplateRefExtractor)(13, TextareaLabelComponent_ng_container_1_ng_container_13_Template, 4, 2, "ng-container", 3);
    ɵɵelementEnd();
    ɵɵtemplate(14, TextareaLabelComponent_ng_container_1_ng_container_14_Template, 4, 3, "ng-container", 3);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const labelContent_r2 = ɵɵreference(5);
    const textAreaContent_r3 = ɵɵreference(12);
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵproperty("for", ctx_r0.labelInputID)("ngClass", ɵɵpureFunction1(13, _c3, ctx_r0.disabled));
    ɵɵattribute("aria-label", ctx_r0.ariaLabel);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.labelTemplate)("ngIfElse", labelContent_r2);
    ɵɵadvance(3);
    ɵɵproperty("ngClass", ɵɵpureFunction1(15, _c4, ctx_r0.warn));
    ɵɵattribute("data-invalid", ctx_r0.invalid ? true : null);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", !ctx_r0.fluid && ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.fluid && !ctx_r0.invalid && ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.textAreaTemplate)("ngIfElse", textAreaContent_r3);
    ɵɵadvance(3);
    ɵɵproperty("ngIf", ctx_r0.fluid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.fluid);
  }
}
var _c6 = ["*", [["", "cdsText", ""], ["", "ibmText", ""], ["input", "type", "text"], ["div"]]];
var _c7 = ["*", "[cdsText],[ibmText],input[type=text],div"];
var _c8 = (a0) => ({
  "cds--text-input__field-wrapper--warning": a0
});
function TextInputLabelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "span", 6)(2, "div", 7);
    ɵɵelementContainerEnd();
  }
}
function TextInputLabelComponent_label_1_1_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_label_1_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_label_1_1_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.labelTemplate);
  }
}
function TextInputLabelComponent_label_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function TextInputLabelComponent_label_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "label", 8);
    ɵɵtemplate(1, TextInputLabelComponent_label_1_1_Template, 1, 1, null, 9)(2, TextInputLabelComponent_label_1_ng_template_2_Template, 1, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const labelContent_r2 = ɵɵreference(3);
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("for", ctx_r0.labelInputID)("ngClass", ɵɵpureFunction1(5, _c3, ctx_r0.disabled));
    ɵɵattribute("aria-label", ctx_r0.ariaLabel);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.labelTemplate)("ngIfElse", labelContent_r2);
  }
}
function TextInputLabelComponent_div_2__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 15);
  }
}
function TextInputLabelComponent_div_2__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 16);
  }
}
function TextInputLabelComponent_div_2_5_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_div_2_5_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.textInputTemplate);
  }
}
function TextInputLabelComponent_div_2_ng_template_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0, 1);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 19);
    ɵɵtemplate(1, TextInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_8_div_2_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_8_div_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 19);
    ɵɵtemplate(1, TextInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_8_div_3_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextInputLabelComponent_div_2_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "hr", 17);
    ɵɵtemplate(2, TextInputLabelComponent_div_2_ng_container_8_div_2_Template, 3, 2, "div", 18)(3, TextInputLabelComponent_div_2_ng_container_8_div_3_Template, 3, 2, "div", 18);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.helperText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_div_2_ng_container_9_div_1_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 21);
    ɵɵtemplate(1, TextInputLabelComponent_div_2_ng_container_9_div_1_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_9_div_1_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngClass", ɵɵpureFunction1(3, _c5, ctx_r0.disabled));
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_div_2_ng_container_9_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 19);
    ɵɵtemplate(1, TextInputLabelComponent_div_2_ng_container_9_div_2_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_9_div_2_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_2_ng_template_0_Template(rf, ctx) {
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TextInputLabelComponent_div_2_ng_container_9_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function TextInputLabelComponent_div_2_ng_container_9_div_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 19);
    ɵɵtemplate(1, TextInputLabelComponent_div_2_ng_container_9_div_3_ng_container_1_Template, 2, 1, "ng-container", 3)(2, TextInputLabelComponent_div_2_ng_container_9_div_3_2_Template, 1, 1, null, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function TextInputLabelComponent_div_2_ng_container_9_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, TextInputLabelComponent_div_2_ng_container_9_div_1_Template, 3, 5, "div", 20)(2, TextInputLabelComponent_div_2_ng_container_9_div_2_Template, 3, 2, "div", 18)(3, TextInputLabelComponent_div_2_ng_container_9_div_3_Template, 3, 2, "div", 18);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function TextInputLabelComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 11)(1, "div", 12, 1);
    ɵɵtemplate(3, TextInputLabelComponent_div_2__svg_svg_3_Template, 1, 0, "svg", 13)(4, TextInputLabelComponent_div_2__svg_svg_4_Template, 1, 0, "svg", 14)(5, TextInputLabelComponent_div_2_5_Template, 1, 1, null, 9)(6, TextInputLabelComponent_div_2_ng_template_6_Template, 1, 0, "ng-template", null, 2, ɵɵtemplateRefExtractor)(8, TextInputLabelComponent_div_2_ng_container_8_Template, 4, 2, "ng-container", 3);
    ɵɵelementEnd();
    ɵɵtemplate(9, TextInputLabelComponent_div_2_ng_container_9_Template, 4, 3, "ng-container", 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const textInputContent_r3 = ɵɵreference(7);
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵproperty("ngClass", ɵɵpureFunction1(8, _c8, ctx_r0.warn));
    ɵɵattribute("data-invalid", ctx_r0.invalid ? true : null);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", ctx_r0.invalid && !ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.textInputTemplate)("ngIfElse", textInputContent_r3);
    ɵɵadvance(3);
    ɵɵproperty("ngIf", ctx_r0.fluid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.fluid);
  }
}
var _c9 = ["*", [["", "cdsPassword", ""], ["", "ibmPassword", ""]]];
var _c10 = ["*", "[cdsPassword], [ibmPassword]"];
function PasswordInputLabelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "span", 5)(2, "div", 6);
    ɵɵelementContainerEnd();
  }
}
function PasswordInputLabelComponent_label_1_1_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_label_1_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PasswordInputLabelComponent_label_1_1_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.labelTemplate);
  }
}
function PasswordInputLabelComponent_label_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function PasswordInputLabelComponent_label_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "label", 7);
    ɵɵtemplate(1, PasswordInputLabelComponent_label_1_1_Template, 1, 1, null, 8)(2, PasswordInputLabelComponent_label_1_ng_template_2_Template, 1, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const labelContent_r2 = ɵɵreference(3);
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("for", ctx_r0.labelInputID)("ngClass", ɵɵpureFunction1(5, _c3, ctx_r0.disabled));
    ɵɵattribute("aria-label", ctx_r0.ariaLabel);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.labelTemplate)("ngIfElse", labelContent_r2);
  }
}
function PasswordInputLabelComponent_div_2__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 15);
  }
}
function PasswordInputLabelComponent_div_2__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 16);
  }
}
function PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 22);
  }
}
function PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 23);
  }
}
function PasswordInputLabelComponent_div_2_cds_tooltip_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "cds-tooltip", 17)(1, "div", 18)(2, "button", 19);
    ɵɵlistener("click", function PasswordInputLabelComponent_div_2_cds_tooltip_6_Template_button_click_2_listener($event) {
      ɵɵrestoreView(_r3);
      const ctx_r0 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r0.handleTogglePasswordVisibility($event));
    });
    ɵɵtemplate(3, PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_3_Template, 1, 0, "svg", 20)(4, PasswordInputLabelComponent_div_2_cds_tooltip_6__svg_svg_4_Template, 1, 0, "svg", 21);
    ɵɵelementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("description", ctx_r0.passwordIsVisible ? ctx_r0.hidePasswordLabel : ctx_r0.showPasswordLabel)("disabled", ctx_r0.disabled)("caret", ctx_r0.caret)("dropShadow", ctx_r0.dropShadow)("highContrast", ctx_r0.highContrast)("isOpen", ctx_r0.isOpen)("align", ctx_r0.align)("autoAlign", ctx_r0.autoAlign)("enterDelayMs", ctx_r0.enterDelayMs)("leaveDelayMs", ctx_r0.leaveDelayMs);
    ɵɵadvance(2);
    ɵɵproperty("disabled", ctx_r0.disabled);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.passwordIsVisible);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.passwordIsVisible);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 26);
    ɵɵtemplate(1, PasswordInputLabelComponent_div_2_ng_container_7_div_2_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_7_div_2_2_Template, 1, 1, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_div_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 26);
    ɵɵtemplate(1, PasswordInputLabelComponent_div_2_ng_container_7_div_3_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_7_div_3_2_Template, 1, 1, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "hr", 24);
    ɵɵtemplate(2, PasswordInputLabelComponent_div_2_ng_container_7_div_2_Template, 3, 2, "div", 25)(3, PasswordInputLabelComponent_div_2_ng_container_7_div_3_Template, 3, 2, "div", 25);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", !ctx_r0.warn && ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.helperText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 28);
    ɵɵtemplate(1, PasswordInputLabelComponent_div_2_ng_container_8_div_1_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_1_2_Template, 1, 1, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngClass", ɵɵpureFunction1(3, _c5, ctx_r0.disabled));
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 26);
    ɵɵtemplate(1, PasswordInputLabelComponent_div_2_ng_container_8_div_2_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_2_2_Template, 1, 1, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template(rf, ctx) {
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_ng_template_0_Template, 0, 0, "ng-template", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_div_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 26);
    ɵɵtemplate(1, PasswordInputLabelComponent_div_2_ng_container_8_div_3_ng_container_1_Template, 2, 1, "ng-container", 2)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_3_2_Template, 1, 1, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function PasswordInputLabelComponent_div_2_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, PasswordInputLabelComponent_div_2_ng_container_8_div_1_Template, 3, 5, "div", 27)(2, PasswordInputLabelComponent_div_2_ng_container_8_div_2_Template, 3, 2, "div", 25)(3, PasswordInputLabelComponent_div_2_ng_container_8_div_3_Template, 3, 2, "div", 25);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.skeleton && ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.warn && ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
function PasswordInputLabelComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 10)(1, "div", 11, 1);
    ɵɵtemplate(3, PasswordInputLabelComponent_div_2__svg_svg_3_Template, 1, 0, "svg", 12)(4, PasswordInputLabelComponent_div_2__svg_svg_4_Template, 1, 0, "svg", 13);
    ɵɵprojection(5, 1);
    ɵɵtemplate(6, PasswordInputLabelComponent_div_2_cds_tooltip_6_Template, 5, 13, "cds-tooltip", 14)(7, PasswordInputLabelComponent_div_2_ng_container_7_Template, 4, 2, "ng-container", 2);
    ɵɵelementEnd();
    ɵɵtemplate(8, PasswordInputLabelComponent_div_2_ng_container_8_Template, 4, 3, "ng-container", 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵproperty("ngClass", ɵɵpureFunction1(7, _c8, ctx_r0.warn));
    ɵɵattribute("data-invalid", ctx_r0.invalid ? true : null);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", !ctx_r0.warn && ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", !ctx_r0.skeleton);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.fluid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.fluid);
  }
}
var _c11 = [[["input"], ["textarea"], ["div"]], "*"];
var _c12 = ["input,textarea,div", "*"];
var _c13 = (a0, a1) => ({
  "cds--label--disabled": a0,
  "cds--skeleton": a1
});
function Label_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function Label_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0, 1);
  }
}
function Label_ng_container_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "cds-textarea-label", 7);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    const inputContentTemplate_r2 = ɵɵreference(1);
    const labelContentTemplate_r3 = ɵɵreference(3);
    ɵɵadvance();
    ɵɵproperty("labelInputID", ctx_r0.labelInputID)("disabled", ctx_r0.disabled)("skeleton", ctx_r0.skeleton)("helperText", ctx_r0.helperText)("invalid", ctx_r0.invalid)("invalidText", ctx_r0.invalidText)("warn", ctx_r0.warn)("warnText", ctx_r0.warnText);
    ɵɵariaProperty("ariaLabel", ctx_r0.ariaLabel);
    ɵɵproperty("labelTemplate", labelContentTemplate_r3)("textAreaTemplate", inputContentTemplate_r2);
  }
}
function Label_ng_container_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "cds-text-label", 8);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    const inputContentTemplate_r2 = ɵɵreference(1);
    const labelContentTemplate_r3 = ɵɵreference(3);
    ɵɵadvance();
    ɵɵproperty("labelInputID", ctx_r0.labelInputID)("disabled", ctx_r0.disabled)("skeleton", ctx_r0.skeleton)("helperText", ctx_r0.helperText)("invalid", ctx_r0.invalid)("invalidText", ctx_r0.invalidText)("warn", ctx_r0.warn)("warnText", ctx_r0.warnText);
    ɵɵariaProperty("ariaLabel", ctx_r0.ariaLabel);
    ɵɵproperty("labelTemplate", labelContentTemplate_r3)("textInputTemplate", inputContentTemplate_r2);
  }
}
function Label_ng_container_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelement(1, "cds-password-label", 9);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    const inputContentTemplate_r2 = ɵɵreference(1);
    const labelContentTemplate_r3 = ɵɵreference(3);
    ɵɵadvance();
    ɵɵproperty("labelInputID", ctx_r0.labelInputID)("disabled", ctx_r0.disabled)("skeleton", ctx_r0.skeleton)("helperText", ctx_r0.helperText)("invalid", ctx_r0.invalid)("invalidText", ctx_r0.invalidText)("warn", ctx_r0.warn)("warnText", ctx_r0.warnText);
    ɵɵariaProperty("ariaLabel", ctx_r0.ariaLabel);
    ɵɵproperty("labelTemplate", labelContentTemplate_r3)("passwordInputTemplate", inputContentTemplate_r2);
  }
}
function Label_ng_container_8_ng_template_1_Template(rf, ctx) {
}
function Label_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, Label_ng_container_8_ng_template_1_Template, 0, 0, "ng-template", 10);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    ɵɵnextContext();
    const default_r4 = ɵɵreference(10);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", default_r4);
  }
}
function Label_ng_template_9_ng_template_1_Template(rf, ctx) {
}
function Label_ng_template_9__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 17);
  }
}
function Label_ng_template_9__svg_svg_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelement(0, "svg", 18);
  }
}
function Label_ng_template_9_ng_template_6_Template(rf, ctx) {
}
function Label_ng_template_9_div_7_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.helperText);
  }
}
function Label_ng_template_9_div_7_2_ng_template_0_Template(rf, ctx) {
}
function Label_ng_template_9_div_7_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, Label_ng_template_9_div_7_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.helperText);
  }
}
function Label_ng_template_9_div_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 19);
    ɵɵtemplate(1, Label_ng_template_9_div_7_ng_container_1_Template, 2, 1, "ng-container", 20)(2, Label_ng_template_9_div_7_2_Template, 1, 1, null, 20);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngClass", ɵɵpureFunction1(3, _c5, ctx_r0.disabled));
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.helperText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.helperText));
  }
}
function Label_ng_template_9_div_8_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.invalidText);
  }
}
function Label_ng_template_9_div_8_2_ng_template_0_Template(rf, ctx) {
}
function Label_ng_template_9_div_8_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, Label_ng_template_9_div_8_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.invalidText);
  }
}
function Label_ng_template_9_div_8_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 21);
    ɵɵtemplate(1, Label_ng_template_9_div_8_ng_container_1_Template, 2, 1, "ng-container", 20)(2, Label_ng_template_9_div_8_2_Template, 1, 1, null, 20);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.invalidText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.invalidText));
  }
}
function Label_ng_template_9_div_9_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.warnText);
  }
}
function Label_ng_template_9_div_9_2_ng_template_0_Template(rf, ctx) {
}
function Label_ng_template_9_div_9_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, Label_ng_template_9_div_9_2_ng_template_0_Template, 0, 0, "ng-template", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.warnText);
  }
}
function Label_ng_template_9_div_9_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 21);
    ɵɵtemplate(1, Label_ng_template_9_div_9_ng_container_1_Template, 2, 1, "ng-container", 20)(2, Label_ng_template_9_div_9_2_Template, 1, 1, null, 20);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.isTemplate(ctx_r0.warnText));
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.isTemplate(ctx_r0.warnText));
  }
}
function Label_ng_template_9_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "label", 11);
    ɵɵtemplate(1, Label_ng_template_9_ng_template_1_Template, 0, 0, "ng-template", 10);
    ɵɵelementEnd();
    ɵɵelementStart(2, "div", 12, 3);
    ɵɵtemplate(4, Label_ng_template_9__svg_svg_4_Template, 1, 0, "svg", 13)(5, Label_ng_template_9__svg_svg_5_Template, 1, 0, "svg", 14)(6, Label_ng_template_9_ng_template_6_Template, 0, 0, "ng-template", 10);
    ɵɵelementEnd();
    ɵɵtemplate(7, Label_ng_template_9_div_7_Template, 3, 5, "div", 15)(8, Label_ng_template_9_div_8_Template, 3, 2, "div", 16)(9, Label_ng_template_9_div_9_Template, 3, 2, "div", 16);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    const inputContentTemplate_r2 = ɵɵreference(1);
    const labelContentTemplate_r3 = ɵɵreference(3);
    ɵɵproperty("for", ctx_r0.labelInputID)("ngClass", ɵɵpureFunction2(12, _c13, ctx_r0.disabled, ctx_r0.skeleton));
    ɵɵattribute("aria-label", ctx_r0.ariaLabel);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", labelContentTemplate_r3);
    ɵɵadvance();
    ɵɵproperty("ngClass", ɵɵpureFunction1(15, _c8, ctx_r0.warn));
    ɵɵattribute("data-invalid", ctx_r0.invalid ? true : null);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", inputContentTemplate_r2);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.skeleton && ctx_r0.helperText && !ctx_r0.invalid && !ctx_r0.warn);
    ɵɵadvance();
    ɵɵproperty("ngIf", ctx_r0.invalid);
    ɵɵadvance();
    ɵɵproperty("ngIf", !ctx_r0.invalid && ctx_r0.warn);
  }
}
var TextInput = class {
  constructor() {
    this.theme = "dark";
    this.size = "md";
    this.inputClass = true;
    this.invalid = false;
    this.warn = false;
    this.skeleton = false;
  }
  /**
   * @todo - remove `cds--text-input--${size}` classes in v12
   */
  get isSizeSm() {
    return this.size === "sm";
  }
  get isSizeMd() {
    return this.size === "md";
  }
  get isSizelg() {
    return this.size === "lg";
  }
  // Size
  get sizeSm() {
    return this.size === "sm";
  }
  get sizeMd() {
    return this.size === "md";
  }
  get sizelg() {
    return this.size === "lg";
  }
  get isLightTheme() {
    return this.theme === "light";
  }
  get getInvalidAttribute() {
    return this.invalid ? true : void 0;
  }
};
TextInput.ɵfac = function TextInput_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextInput)();
};
TextInput.ɵdir = ɵɵdefineDirective({
  type: TextInput,
  selectors: [["", "cdsText", ""], ["", "ibmText", ""]],
  hostVars: 23,
  hostBindings: function TextInput_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵattribute("data-invalid", ctx.getInvalidAttribute);
      ɵɵclassProp("cds--text-input", ctx.inputClass)("cds--text-input--sm", ctx.isSizeSm)("cds--text-input--md", ctx.isSizeMd)("cds--text-input--lg", ctx.isSizelg)("cds--layout--size-sm", ctx.sizeSm)("cds--layout--size-md", ctx.sizeMd)("cds--layout--size-lg", ctx.sizelg)("cds--text-input--invalid", ctx.invalid)("cds--text-input--warning", ctx.warn)("cds--skeleton", ctx.skeleton)("cds--text-input--light", ctx.isLightTheme);
    }
  },
  inputs: {
    theme: "theme",
    size: "size",
    invalid: "invalid",
    warn: "warn",
    skeleton: "skeleton"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextInput, [{
    type: Directive,
    args: [{
      selector: "[cdsText], [ibmText]"
    }]
  }], null, {
    theme: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    inputClass: [{
      type: HostBinding,
      args: ["class.cds--text-input"]
    }],
    isSizeSm: [{
      type: HostBinding,
      args: ["class.cds--text-input--sm"]
    }],
    isSizeMd: [{
      type: HostBinding,
      args: ["class.cds--text-input--md"]
    }],
    isSizelg: [{
      type: HostBinding,
      args: ["class.cds--text-input--lg"]
    }],
    sizeSm: [{
      type: HostBinding,
      args: ["class.cds--layout--size-sm"]
    }],
    sizeMd: [{
      type: HostBinding,
      args: ["class.cds--layout--size-md"]
    }],
    sizelg: [{
      type: HostBinding,
      args: ["class.cds--layout--size-lg"]
    }],
    invalid: [{
      type: HostBinding,
      args: ["class.cds--text-input--invalid"]
    }, {
      type: Input
    }],
    warn: [{
      type: HostBinding,
      args: ["class.cds--text-input--warning"]
    }, {
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    isLightTheme: [{
      type: HostBinding,
      args: ["class.cds--text-input--light"]
    }],
    getInvalidAttribute: [{
      type: HostBinding,
      args: ["attr.data-invalid"]
    }]
  });
})();
var TextArea = class {
  constructor() {
    this.theme = "dark";
    this.baseClass = true;
    this.invalid = false;
    this.skeleton = false;
  }
  get isLightTheme() {
    return this.theme === "light";
  }
  get getInvalidAttr() {
    return this.invalid ? true : void 0;
  }
};
TextArea.ɵfac = function TextArea_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextArea)();
};
TextArea.ɵdir = ɵɵdefineDirective({
  type: TextArea,
  selectors: [["", "cdsTextArea", ""], ["", "ibmTextArea", ""]],
  hostVars: 9,
  hostBindings: function TextArea_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵattribute("data-invalid", ctx.getInvalidAttr);
      ɵɵclassProp("cds--text-area", ctx.baseClass)("cds--text-area--invalid", ctx.invalid)("cds--skeleton", ctx.skeleton)("cds--text-area--light", ctx.isLightTheme);
    }
  },
  inputs: {
    theme: "theme",
    invalid: "invalid",
    skeleton: "skeleton"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextArea, [{
    type: Directive,
    args: [{
      selector: "[cdsTextArea], [ibmTextArea]"
    }]
  }], null, {
    theme: [{
      type: Input
    }],
    baseClass: [{
      type: HostBinding,
      args: ["class.cds--text-area"]
    }],
    invalid: [{
      type: HostBinding,
      args: ["class.cds--text-area--invalid"]
    }, {
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    isLightTheme: [{
      type: HostBinding,
      args: ["class.cds--text-area--light"]
    }],
    getInvalidAttr: [{
      type: HostBinding,
      args: ["attr.data-invalid"]
    }]
  });
})();
var PasswordInput = class {
  constructor(elementRef, renderer) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.passwordInputClass = true;
    this.inputClass = true;
    this.invalid = false;
    this.warn = false;
    this.skeleton = false;
    this.theme = "dark";
    this.size = "md";
    this._type = "password";
  }
  set type(type) {
    if (type) {
      this._type = type;
      if (this.elementRef) {
        this.renderer.setAttribute(this.elementRef.nativeElement, "type", this._type);
      }
    }
  }
  /**
   * @todo - remove `cds--text-input--${size}` classes in v12
   */
  get isSizeSm() {
    return this.size === "sm";
  }
  get isSizeMd() {
    return this.size === "md";
  }
  get isSizelg() {
    return this.size === "lg";
  }
  // Size
  get sizeSm() {
    return this.size === "sm";
  }
  get sizeMd() {
    return this.size === "md";
  }
  get sizelg() {
    return this.size === "lg";
  }
  get isLightTheme() {
    return this.theme === "light";
  }
  get getInvalidAttribute() {
    return this.invalid ? true : void 0;
  }
  ngAfterViewInit() {
    this.renderer.setAttribute(this.elementRef.nativeElement, "type", this._type);
  }
};
PasswordInput.ɵfac = function PasswordInput_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PasswordInput)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2));
};
PasswordInput.ɵdir = ɵɵdefineDirective({
  type: PasswordInput,
  selectors: [["", "cdsPassword", ""], ["", "ibmPassword", ""]],
  hostVars: 25,
  hostBindings: function PasswordInput_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵattribute("data-invalid", ctx.getInvalidAttribute);
      ɵɵclassProp("cds--password-input", ctx.passwordInputClass)("cds--text-input--sm", ctx.isSizeSm)("cds--text-input--md", ctx.isSizeMd)("cds--text-input--lg", ctx.isSizelg)("cds--layout--size-sm", ctx.sizeSm)("cds--layout--size-md", ctx.sizeMd)("cds--layout--size-lg", ctx.sizelg)("cds--text-input--light", ctx.isLightTheme)("cds--text-input", ctx.inputClass)("cds--text-input--invalid", ctx.invalid)("cds--text-input--warning", ctx.warn)("cds--skeleton", ctx.skeleton);
    }
  },
  inputs: {
    type: "type",
    invalid: "invalid",
    warn: "warn",
    skeleton: "skeleton",
    theme: "theme",
    size: "size"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PasswordInput, [{
    type: Directive,
    args: [{
      selector: "[cdsPassword], [ibmPassword]"
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: Renderer2
    }];
  }, {
    type: [{
      type: Input
    }],
    passwordInputClass: [{
      type: HostBinding,
      args: ["class.cds--password-input"]
    }],
    isSizeSm: [{
      type: HostBinding,
      args: ["class.cds--text-input--sm"]
    }],
    isSizeMd: [{
      type: HostBinding,
      args: ["class.cds--text-input--md"]
    }],
    isSizelg: [{
      type: HostBinding,
      args: ["class.cds--text-input--lg"]
    }],
    sizeSm: [{
      type: HostBinding,
      args: ["class.cds--layout--size-sm"]
    }],
    sizeMd: [{
      type: HostBinding,
      args: ["class.cds--layout--size-md"]
    }],
    sizelg: [{
      type: HostBinding,
      args: ["class.cds--layout--size-lg"]
    }],
    isLightTheme: [{
      type: HostBinding,
      args: ["class.cds--text-input--light"]
    }],
    inputClass: [{
      type: HostBinding,
      args: ["class.cds--text-input"]
    }],
    invalid: [{
      type: HostBinding,
      args: ["class.cds--text-input--invalid"]
    }, {
      type: Input
    }],
    warn: [{
      type: HostBinding,
      args: ["class.cds--text-input--warning"]
    }, {
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    theme: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    getInvalidAttribute: [{
      type: HostBinding,
      args: ["attr.data-invalid"]
    }]
  });
})();
var TextareaLabelComponent = class _TextareaLabelComponent {
  /**
   * Creates an instance of Label.
   */
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = "ibm-textarea-" + _TextareaLabelComponent.labelCounter;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
    this.fluid = false;
    this.labelClass = true;
  }
  get isReadonly() {
    return this.wrapper?.nativeElement.querySelector("textarea")?.readOnly ?? false;
  }
  get fluidClass() {
    return this.fluid && !this.skeleton;
  }
  get fluidSkeletonClass() {
    return this.fluid && this.skeleton;
  }
  /**
   * Sets the id on the input item associated with the `Label`.
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("textarea");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
      const divElement = this.wrapper.nativeElement.querySelector("div");
      if (divElement) {
        if (divElement.id) {
          this.labelInputID = divElement.id;
          this.changeDetectorRef.detectChanges();
        }
        divElement.setAttribute("id", this.labelInputID);
      }
    }
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TextareaLabelComponent.labelCounter = 0;
TextareaLabelComponent.ɵfac = function TextareaLabelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextareaLabelComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
};
TextareaLabelComponent.ɵcmp = ɵɵdefineComponent({
  type: TextareaLabelComponent,
  selectors: [["cds-textarea-label"], ["ibm-textarea-label"]],
  contentQueries: function TextareaLabelComponent_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      ɵɵcontentQuery(dirIndex, TextArea, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.textArea = _t.first);
    }
  },
  viewQuery: function TextareaLabelComponent_Query(rf, ctx) {
    if (rf & 1) {
      ɵɵviewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 8,
  hostBindings: function TextareaLabelComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--form-item", ctx.labelClass)("cds--text-area__wrapper--readonly", ctx.isReadonly)("cds--text-area--fluid", ctx.fluidClass)("cds--text-area--fluid__skeleton", ctx.fluidSkeletonClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    labelTemplate: "labelTemplate",
    textAreaTemplate: "textAreaTemplate",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel",
    fluid: "fluid"
  },
  standalone: false,
  ngContentSelectors: _c2,
  decls: 2,
  vars: 2,
  consts: [["labelContent", ""], ["wrapper", ""], ["textAreaContent", ""], [4, "ngIf"], [1, "cds--label", "cds--skeleton"], [1, "cds--text-area", "cds--skeleton"], [1, "cds--text-area__label-wrapper"], [1, "cds--label", 3, "for", "ngClass"], [4, "ngIf", "ngIfElse"], [1, "cds--text-area__wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-area__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-area__invalid-icon cds--text-area__invalid-icon--warning", 4, "ngIf"], [3, "ngTemplateOutlet"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-area__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-area__invalid-icon", "cds--text-area__invalid-icon--warning"], [1, "cds--text-area__divider"], ["class", "cds--form-requirement", 4, "ngIf"], [1, "cds--form-requirement"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], [1, "cds--form__helper-text", 3, "ngClass"]],
  template: function TextareaLabelComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef(_c1);
      ɵɵtemplate(0, TextareaLabelComponent_ng_container_0_Template, 3, 0, "ng-container", 3)(1, TextareaLabelComponent_ng_container_1_Template, 15, 17, "ng-container", 3);
    }
    if (rf & 2) {
      ɵɵproperty("ngIf", ctx.skeleton);
      ɵɵadvance();
      ɵɵproperty("ngIf", !ctx.skeleton);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, IconDirective],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextareaLabelComponent, [{
    type: Component,
    args: [{
      selector: "cds-textarea-label, ibm-textarea-label",
      template: `
		<ng-container *ngIf="skeleton">
			<span class="cds--label cds--skeleton"></span>
			<div class="cds--text-area cds--skeleton"></div>
		</ng-container>
		<ng-container *ngIf="!skeleton">
			<div class="cds--text-area__label-wrapper">
				<label
					[for]="labelInputID"
					[attr.aria-label]="ariaLabel"
					class="cds--label"
					[ngClass]="{
						'cds--label--disabled': disabled
					}">
					<ng-template *ngIf="labelTemplate; else labelContent" [ngTemplateOutlet]="labelTemplate"></ng-template>
					<ng-template #labelContent>
						<ng-content></ng-content>
					</ng-template>
				</label>
			</div>
			<div
				class="cds--text-area__wrapper"
				[ngClass]="{
					'cds--text-area__wrapper--warn': warn
				}"
				[attr.data-invalid]="(invalid ? true : null)"
				#wrapper>
				<svg
					*ngIf="!fluid && invalid"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-area__invalid-icon">
				</svg>
				<svg
					*ngIf="!fluid && !invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-area__invalid-icon cds--text-area__invalid-icon--warning">
				</svg>
				<ng-template *ngIf="textAreaTemplate; else textAreaContent" [ngTemplateOutlet]="textAreaTemplate"></ng-template>
				<ng-template #textAreaContent>
					<ng-content select="[cdsTextArea],[ibmTextArea],textarea"></ng-content>
				</ng-template>

				<ng-container *ngIf="fluid">
					<hr class="cds--text-area__divider" />
					<div *ngIf="invalid" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
						<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
						<svg
							cdsIcon="warning--filled"
							size="16"
							class="cds--text-area__invalid-icon">
						</svg>
					</div>
					<div *ngIf="!invalid && warn" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
						<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
						<svg
							cdsIcon="warning--alt--filled"
							size="16"
							class="cds--text-area__invalid-icon cds--text-area__invalid-icon--warning">
						</svg>
					</div>
				</ng-container>
			</div>
			<ng-container *ngIf="!fluid">
				<div
					*ngIf="helperText && !invalid && !warn"
					class="cds--form__helper-text"
					[ngClass]="{'cds--form__helper-text--disabled': disabled}">
					<ng-container *ngIf="!isTemplate(helperText)">{{helperText}}</ng-container>
					<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
				</div>
				<div *ngIf="invalid" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
					<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
				</div>
				<div *ngIf="!invalid && warn" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
					<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
				</div>
			</ng-container>
		</ng-container>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    labelTemplate: [{
      type: Input
    }],
    textAreaTemplate: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    fluid: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper", {
        static: false
      }]
    }],
    textArea: [{
      type: ContentChild,
      args: [TextArea, {
        static: false
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }],
    isReadonly: [{
      type: HostBinding,
      args: ["class.cds--text-area__wrapper--readonly"]
    }],
    fluidClass: [{
      type: HostBinding,
      args: ["class.cds--text-area--fluid"]
    }],
    fluidSkeletonClass: [{
      type: HostBinding,
      args: ["class.cds--text-area--fluid__skeleton"]
    }]
  });
})();
var TextInputLabelComponent = class _TextInputLabelComponent {
  /**
   * Creates an instance of Label.
   */
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = "ibm-text-input-" + _TextInputLabelComponent.labelCounter++;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
    this.fluid = false;
    this.labelClass = true;
    this.textInputWrapper = true;
  }
  get isReadonly() {
    return this.wrapper?.nativeElement.querySelector("input")?.readOnly ?? false;
  }
  get fluidClass() {
    return this.fluid && !this.skeleton;
  }
  get fluidSkeletonClass() {
    return this.fluid && this.skeleton;
  }
  /**
   * Sets the id on the input item associated with the `Label`.
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("input");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
      const divElement = this.wrapper.nativeElement.querySelector("div");
      if (divElement) {
        if (divElement.id) {
          this.labelInputID = divElement.id;
          this.changeDetectorRef.detectChanges();
        }
        divElement.setAttribute("id", this.labelInputID);
      }
    }
  }
  ngAfterContentInit() {
    this.changeDetectorRef.detectChanges();
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TextInputLabelComponent.labelCounter = 0;
TextInputLabelComponent.ɵfac = function TextInputLabelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TextInputLabelComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
};
TextInputLabelComponent.ɵcmp = ɵɵdefineComponent({
  type: TextInputLabelComponent,
  selectors: [["cds-text-label"], ["ibm-text-label"]],
  viewQuery: function TextInputLabelComponent_Query(rf, ctx) {
    if (rf & 1) {
      ɵɵviewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 10,
  hostBindings: function TextInputLabelComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--form-item", ctx.labelClass)("cds--text-input-wrapper", ctx.textInputWrapper)("cds--text-input-wrapper--readonly", ctx.isReadonly)("cds--text-input--fluid", ctx.fluidClass)("cds--text-input--fluid__skeleton", ctx.fluidSkeletonClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    labelTemplate: "labelTemplate",
    textInputTemplate: "textInputTemplate",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel",
    fluid: "fluid"
  },
  standalone: false,
  ngContentSelectors: _c7,
  decls: 3,
  vars: 3,
  consts: [["labelContent", ""], ["wrapper", ""], ["textInputContent", ""], [4, "ngIf"], ["class", "cds--label", 3, "for", "ngClass", 4, "ngIf"], ["class", "cds--text-input__field-outer-wrapper", 4, "ngIf"], [1, "cds--label", "cds--skeleton"], [1, "cds--text-input", "cds--skeleton"], [1, "cds--label", 3, "for", "ngClass"], [4, "ngIf", "ngIfElse"], [3, "ngTemplateOutlet"], [1, "cds--text-input__field-outer-wrapper"], [1, "cds--text-input__field-wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-input__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-input__invalid-icon cds--text-input__invalid-icon--warning", 4, "ngIf"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-input__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-input__invalid-icon", "cds--text-input__invalid-icon--warning"], [1, "cds--text-input__divider"], ["class", "cds--form-requirement", 4, "ngIf"], [1, "cds--form-requirement"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], [1, "cds--form__helper-text", 3, "ngClass"]],
  template: function TextInputLabelComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef(_c6);
      ɵɵtemplate(0, TextInputLabelComponent_ng_container_0_Template, 3, 0, "ng-container", 3)(1, TextInputLabelComponent_label_1_Template, 4, 7, "label", 4)(2, TextInputLabelComponent_div_2_Template, 10, 10, "div", 5);
    }
    if (rf & 2) {
      ɵɵproperty("ngIf", ctx.skeleton);
      ɵɵadvance();
      ɵɵproperty("ngIf", !ctx.skeleton);
      ɵɵadvance();
      ɵɵproperty("ngIf", !ctx.skeleton);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, IconDirective],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TextInputLabelComponent, [{
    type: Component,
    args: [{
      selector: "cds-text-label, ibm-text-label",
      template: `
		<ng-container *ngIf="skeleton">
			<span class="cds--label cds--skeleton"></span>
			<div class="cds--text-input cds--skeleton"></div>
		</ng-container>
		<label
			*ngIf="!skeleton"
			[for]="labelInputID"
			[attr.aria-label]="ariaLabel"
			class="cds--label"
			[ngClass]="{
				'cds--label--disabled': disabled
			}">
			<ng-template *ngIf="labelTemplate; else labelContent" [ngTemplateOutlet]="labelTemplate"></ng-template>
			<ng-template #labelContent>
				<ng-content></ng-content>
			</ng-template>
		</label>
		<div *ngIf="!skeleton" class="cds--text-input__field-outer-wrapper">
			<div
				class="cds--text-input__field-wrapper"
				[ngClass]="{
					'cds--text-input__field-wrapper--warning': warn
				}"
				[attr.data-invalid]="(invalid ? true : null)"
				#wrapper>
				<svg
					*ngIf="invalid && !warn"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-input__invalid-icon">
				</svg>
				<svg
					*ngIf="!invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-input__invalid-icon cds--text-input__invalid-icon--warning">
				</svg>
				<ng-template *ngIf="textInputTemplate; else textInputContent" [ngTemplateOutlet]="textInputTemplate"></ng-template>
				<ng-template #textInputContent>
					<ng-content select="[cdsText],[ibmText],input[type=text],div"></ng-content>
				</ng-template>

				<ng-container *ngIf="fluid">
					<hr class="cds--text-input__divider" />
					<div *ngIf="invalid" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
						<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
					</div>
					<div *ngIf="!invalid && warn" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
						<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
					</div>
				</ng-container>
			</div>
			<ng-container *ngIf="!fluid">
				<div
					*ngIf="helperText && !invalid && !warn"
					class="cds--form__helper-text"
					[ngClass]="{'cds--form__helper-text--disabled': disabled}">
					<ng-container *ngIf="!isTemplate(helperText)">{{helperText}}</ng-container>
					<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
				</div>
				<div *ngIf="invalid" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
					<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
				</div>
				<div *ngIf="!invalid && warn" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
					<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
				</div>
			</ng-container>
		</div>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    labelTemplate: [{
      type: Input
    }],
    textInputTemplate: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    fluid: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper", {
        static: false
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }],
    textInputWrapper: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper"]
    }],
    isReadonly: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper--readonly"]
    }],
    fluidClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid"]
    }],
    fluidSkeletonClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid__skeleton"]
    }]
  });
})();
var PasswordInputLabelComponent = class _PasswordInputLabelComponent extends BaseIconButton {
  /**
   * Constructor for PasswordInputLabelComponent.
   * @param changeDetectorRef - Reference to ChangeDetectorRef.
   */
  constructor(changeDetectorRef) {
    super();
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = "cds-password-input-" + _PasswordInputLabelComponent.labelCounter++;
    this.inputType = "password";
    this.passwordIsVisible = false;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
    this.hidePasswordLabel = "Hide password";
    this.showPasswordLabel = "Show password";
    this.fluid = false;
    this.labelClass = true;
    this.passwordInputWrapper = true;
    this.textInputWrapper = true;
  }
  get isReadonly() {
    return this.wrapper?.nativeElement.querySelector("input")?.readOnly ?? false;
  }
  get fluidClass() {
    return this.fluid && !this.skeleton;
  }
  get fluidSkeletonClass() {
    return this.fluid && this.skeleton;
  }
  /**
   * Lifecycle hook called after the view has been initialized to set the ID of the input element
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("input");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
    }
  }
  /**
   * Function to check if a value is a TemplateRef.
   * @param value - Value to check.
   * @returns Whether the value is a TemplateRef.
   */
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
  /**
   * Handler for toggling password visibility.
   */
  handleTogglePasswordVisibility() {
    this.inputType = this.inputType === "password" ? "text" : "password";
    this.textInput.type = this.inputType;
    this.passwordIsVisible = this.inputType === "text";
  }
};
PasswordInputLabelComponent.labelCounter = 0;
PasswordInputLabelComponent.ɵfac = function PasswordInputLabelComponent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PasswordInputLabelComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
};
PasswordInputLabelComponent.ɵcmp = ɵɵdefineComponent({
  type: PasswordInputLabelComponent,
  selectors: [["cds-password-label"], ["ibm-password-label"]],
  contentQueries: function PasswordInputLabelComponent_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      ɵɵcontentQuery(dirIndex, PasswordInput, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.textInput = _t.first);
    }
  },
  viewQuery: function PasswordInputLabelComponent_Query(rf, ctx) {
    if (rf & 1) {
      ɵɵviewQuery(_c0, 7);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 12,
  hostBindings: function PasswordInputLabelComponent_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--form-item", ctx.labelClass)("cds--password-input-wrapper", ctx.passwordInputWrapper)("cds--text-input-wrapper", ctx.textInputWrapper)("cds--text-input-wrapper--readonly", ctx.isReadonly)("cds--text-input--fluid", ctx.fluidClass)("cds--text-input--fluid__skeleton", ctx.fluidSkeletonClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    labelTemplate: "labelTemplate",
    passwordInputTemplate: "passwordInputTemplate",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel",
    hidePasswordLabel: "hidePasswordLabel",
    showPasswordLabel: "showPasswordLabel",
    fluid: "fluid"
  },
  standalone: false,
  features: [ɵɵInheritDefinitionFeature],
  ngContentSelectors: _c10,
  decls: 3,
  vars: 3,
  consts: [["labelContent", ""], ["wrapper", ""], [4, "ngIf"], ["class", "cds--label", 3, "for", "ngClass", 4, "ngIf"], ["class", "cds--text-input__field-outer-wrapper", 4, "ngIf"], [1, "cds--label", "cds--skeleton"], [1, "cds--text-input", "cds--skeleton"], [1, "cds--label", 3, "for", "ngClass"], [4, "ngIf", "ngIfElse"], [3, "ngTemplateOutlet"], [1, "cds--text-input__field-outer-wrapper"], [1, "cds--text-input__field-wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-input__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-input__invalid-icon cds--text-input__invalid-icon--warning", 4, "ngIf"], ["class", "cds--toggle-password-tooltip", 3, "description", "disabled", "caret", "dropShadow", "highContrast", "isOpen", "align", "autoAlign", "enterDelayMs", "leaveDelayMs", 4, "ngIf"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-input__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-input__invalid-icon", "cds--text-input__invalid-icon--warning"], [1, "cds--toggle-password-tooltip", 3, "description", "disabled", "caret", "dropShadow", "highContrast", "isOpen", "align", "autoAlign", "enterDelayMs", "leaveDelayMs"], [1, "cds--tooltip-trigger__wrapper"], ["type", "button", 1, "cds--text-input--password__visibility__toggle", "cds--btn", "cds--tooltip__trigger", "cds--tooltip--a11y", 3, "click", "disabled"], ["cdsIcon", "view--off", "class", "cds--icon-visibility-off", "size", "16", 4, "ngIf"], ["cdsIcon", "view", "class", "cds--icon-visibility-on", "size", "16", 4, "ngIf"], ["cdsIcon", "view--off", "size", "16", 1, "cds--icon-visibility-off"], ["cdsIcon", "view", "size", "16", 1, "cds--icon-visibility-on"], [1, "cds--text-input__divider"], ["class", "cds--form-requirement", 4, "ngIf"], [1, "cds--form-requirement"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], [1, "cds--form__helper-text", 3, "ngClass"]],
  template: function PasswordInputLabelComponent_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef(_c9);
      ɵɵtemplate(0, PasswordInputLabelComponent_ng_container_0_Template, 3, 0, "ng-container", 2)(1, PasswordInputLabelComponent_label_1_Template, 4, 7, "label", 3)(2, PasswordInputLabelComponent_div_2_Template, 9, 9, "div", 4);
    }
    if (rf & 2) {
      ɵɵproperty("ngIf", ctx.skeleton);
      ɵɵadvance();
      ɵɵproperty("ngIf", !ctx.skeleton);
      ɵɵadvance();
      ɵɵproperty("ngIf", !ctx.skeleton);
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, IconDirective, Tooltip],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PasswordInputLabelComponent, [{
    type: Component,
    args: [{
      selector: "cds-password-label, ibm-password-label",
      template: `
		<ng-container *ngIf="skeleton">
			<span class="cds--label cds--skeleton"></span>
			<div class="cds--text-input cds--skeleton"></div>
		</ng-container>
		<label
			*ngIf="!skeleton"
			[for]="labelInputID"
			[attr.aria-label]="ariaLabel"
			class="cds--label"
			[ngClass]="{
				'cds--label--disabled': disabled
			}">
			<ng-template *ngIf="labelTemplate; else labelContent" [ngTemplateOutlet]="labelTemplate"></ng-template>
			<ng-template #labelContent>
				<ng-content></ng-content>
			</ng-template>
		</label>

		<div *ngIf="!skeleton" class="cds--text-input__field-outer-wrapper">
			<div
			class="cds--text-input__field-wrapper"
			[ngClass]="{
				'cds--text-input__field-wrapper--warning': warn
			}"
			[attr.data-invalid]="invalid ? true : null"
			#wrapper>
				<svg
					*ngIf="!warn && invalid"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-input__invalid-icon">
				</svg>
				<svg
					*ngIf="!invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-input__invalid-icon cds--text-input__invalid-icon--warning">
				</svg>
				<ng-content select="[cdsPassword], [ibmPassword]"></ng-content>
				<cds-tooltip
					*ngIf="!skeleton"
					[description]="passwordIsVisible ? hidePasswordLabel : showPasswordLabel"
					[disabled]="disabled"
					[caret]="caret"
					[dropShadow]="dropShadow"
					[highContrast]="highContrast"
					[isOpen]="isOpen"
					[align]="align"
					[autoAlign]="autoAlign"
					[enterDelayMs]="enterDelayMs"
					[leaveDelayMs]="leaveDelayMs"
					class="cds--toggle-password-tooltip">
						<div class="cds--tooltip-trigger__wrapper">
							<button
								class="cds--text-input--password__visibility__toggle cds--btn cds--tooltip__trigger cds--tooltip--a11y"
								[disabled]="disabled"
								type="button"
								(click)="handleTogglePasswordVisibility($event)">
								<svg *ngIf="passwordIsVisible" cdsIcon="view--off" class="cds--icon-visibility-off" size="16"></svg>
								<svg *ngIf="!passwordIsVisible" cdsIcon="view" class="cds--icon-visibility-on" size="16"></svg>
							</button>
						</div>
				</cds-tooltip>

				<ng-container *ngIf="fluid">
					<hr class="cds--text-input__divider" />
					<div *ngIf="!warn && invalid" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(invalidText)">{{ invalidText }}</ng-container>
						<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
					</div>
					<div *ngIf="!invalid && warn" class="cds--form-requirement">
						<ng-container *ngIf="!isTemplate(warnText)">{{ warnText }}</ng-container>
						<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
					</div>
				</ng-container>
			</div>
			<ng-container *ngIf="!fluid">
				<div
					*ngIf="!skeleton && helperText && !invalid && !warn"
					class="cds--form__helper-text"
					[ngClass]="{ 'cds--form__helper-text--disabled': disabled }">
					<ng-container *ngIf="!isTemplate(helperText)">{{ helperText }}</ng-container>
					<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
				</div>

				<div *ngIf="!warn && invalid" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(invalidText)">{{ invalidText }}</ng-container>
					<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
				</div>

				<div *ngIf="!invalid && warn" class="cds--form-requirement">
					<ng-container *ngIf="!isTemplate(warnText)">{{ warnText }}</ng-container>
					<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
				</div>
			</ng-container>
		</div>
    `
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    textInput: [{
      type: ContentChild,
      args: [PasswordInput]
    }],
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    labelTemplate: [{
      type: Input
    }],
    passwordInputTemplate: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    hidePasswordLabel: [{
      type: Input
    }],
    showPasswordLabel: [{
      type: Input
    }],
    fluid: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper", {
        static: true
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }],
    passwordInputWrapper: [{
      type: HostBinding,
      args: ["class.cds--password-input-wrapper"]
    }],
    textInputWrapper: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper"]
    }],
    isReadonly: [{
      type: HostBinding,
      args: ["class.cds--text-input-wrapper--readonly"]
    }],
    fluidClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid"]
    }],
    fluidSkeletonClass: [{
      type: HostBinding,
      args: ["class.cds--text-input--fluid__skeleton"]
    }]
  });
})();
var Label = class _Label {
  /**
   * Creates an instance of Label.
   */
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.labelInputID = `cds-label-${_Label.labelCounter++}`;
    this.disabled = false;
    this.skeleton = false;
    this.invalid = false;
    this.warn = false;
  }
  get labelClass() {
    return this.type === void 0;
  }
  /**
   * Update wrapper class if a textarea is hosted.
   */
  ngAfterContentInit() {
    if (this.textArea) {
      this.type = "TextArea";
    } else if (this.textInput) {
      this.type = "TextInput";
    } else if (this.passwordInput) {
      this.type = "PasswordInput";
    }
  }
  /**
   * Sets the id on the input item associated with the `Label`.
   */
  ngAfterViewInit() {
    if (this.wrapper) {
      const inputElement = this.wrapper.nativeElement.querySelector("input,textarea");
      if (inputElement) {
        if (inputElement.id) {
          this.labelInputID = inputElement.id;
          this.changeDetectorRef.detectChanges();
        }
        inputElement.setAttribute("id", this.labelInputID);
        return;
      }
      const divElement = this.wrapper.nativeElement.querySelector("div");
      if (divElement) {
        if (divElement.id) {
          this.labelInputID = divElement.id;
          this.changeDetectorRef.detectChanges();
        }
        divElement.setAttribute("id", this.labelInputID);
      }
    }
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
Label.labelCounter = 0;
Label.ɵfac = function Label_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Label)(ɵɵdirectiveInject(ChangeDetectorRef));
};
Label.ɵcmp = ɵɵdefineComponent({
  type: Label,
  selectors: [["cds-label"], ["ibm-label"]],
  contentQueries: function Label_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      ɵɵcontentQuery(dirIndex, TextArea, 5);
      ɵɵcontentQuery(dirIndex, TextInput, 5);
      ɵɵcontentQuery(dirIndex, PasswordInput, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.textArea = _t.first);
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.textInput = _t.first);
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.passwordInput = _t.first);
    }
  },
  viewQuery: function Label_Query(rf, ctx) {
    if (rf & 1) {
      ɵɵviewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 2,
  hostBindings: function Label_HostBindings(rf, ctx) {
    if (rf & 2) {
      ɵɵclassProp("cds--form-item", ctx.labelClass);
    }
  },
  inputs: {
    labelInputID: "labelInputID",
    disabled: "disabled",
    skeleton: "skeleton",
    helperText: "helperText",
    invalidText: "invalidText",
    invalid: "invalid",
    warn: "warn",
    warnText: "warnText",
    ariaLabel: "ariaLabel"
  },
  standalone: false,
  ngContentSelectors: _c12,
  decls: 11,
  vars: 4,
  consts: [["inputContentTemplate", ""], ["labelContentTemplate", ""], ["default", ""], ["wrapper", ""], [3, "ngSwitch"], [4, "ngSwitchCase"], [4, "ngSwitchDefault"], [3, "labelInputID", "disabled", "skeleton", "helperText", "invalid", "invalidText", "warn", "warnText", "ariaLabel", "labelTemplate", "textAreaTemplate"], [3, "labelInputID", "disabled", "skeleton", "helperText", "invalid", "invalidText", "warn", "warnText", "ariaLabel", "labelTemplate", "textInputTemplate"], [3, "labelInputID", "disabled", "skeleton", "helperText", "invalid", "invalidText", "warn", "warnText", "ariaLabel", "labelTemplate", "passwordInputTemplate"], [3, "ngTemplateOutlet"], [1, "cds--label", 3, "for", "ngClass"], [1, "cds--text-input__field-wrapper", 3, "ngClass"], ["cdsIcon", "warning--filled", "size", "16", "class", "cds--text-input__invalid-icon", 4, "ngIf"], ["cdsIcon", "warning--alt--filled", "size", "16", "class", "cds--text-input__invalid-icon cds--text-input__invalid-icon--warning", 4, "ngIf"], ["class", "cds--form__helper-text", 3, "ngClass", 4, "ngIf"], ["class", "cds--form-requirement", 4, "ngIf"], ["cdsIcon", "warning--filled", "size", "16", 1, "cds--text-input__invalid-icon"], ["cdsIcon", "warning--alt--filled", "size", "16", 1, "cds--text-input__invalid-icon", "cds--text-input__invalid-icon--warning"], [1, "cds--form__helper-text", 3, "ngClass"], [4, "ngIf"], [1, "cds--form-requirement"]],
  template: function Label_Template(rf, ctx) {
    if (rf & 1) {
      ɵɵprojectionDef(_c11);
      ɵɵtemplate(0, Label_ng_template_0_Template, 1, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor)(2, Label_ng_template_2_Template, 1, 0, "ng-template", null, 1, ɵɵtemplateRefExtractor);
      ɵɵelementContainerStart(4, 4);
      ɵɵtemplate(5, Label_ng_container_5_Template, 2, 11, "ng-container", 5)(6, Label_ng_container_6_Template, 2, 11, "ng-container", 5)(7, Label_ng_container_7_Template, 2, 11, "ng-container", 5)(8, Label_ng_container_8_Template, 2, 1, "ng-container", 6);
      ɵɵelementContainerEnd();
      ɵɵtemplate(9, Label_ng_template_9_Template, 10, 17, "ng-template", null, 2, ɵɵtemplateRefExtractor);
    }
    if (rf & 2) {
      ɵɵadvance(4);
      ɵɵproperty("ngSwitch", ctx.type);
      ɵɵadvance();
      ɵɵproperty("ngSwitchCase", "TextArea");
      ɵɵadvance();
      ɵɵproperty("ngSwitchCase", "TextInput");
      ɵɵadvance();
      ɵɵproperty("ngSwitchCase", "PasswordInput");
    }
  },
  dependencies: [NgClass, NgIf, NgTemplateOutlet, NgSwitch, NgSwitchCase, NgSwitchDefault, IconDirective, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Label, [{
    type: Component,
    args: [{
      selector: "cds-label, ibm-label",
      template: `
		<ng-template #inputContentTemplate>
			<ng-content select="input,textarea,div"></ng-content>
		</ng-template>

		<ng-template #labelContentTemplate>
			<ng-content></ng-content>
		</ng-template>

		<ng-container [ngSwitch]="type">
			<ng-container *ngSwitchCase="'TextArea'">
				<cds-textarea-label
					[labelInputID]="labelInputID"
					[disabled]="disabled"
					[skeleton]="skeleton"
					[helperText]="helperText"
					[invalid]="invalid"
					[invalidText]="invalidText"
					[warn]="warn"
					[warnText]="warnText"
					[ariaLabel]="ariaLabel"
					[labelTemplate]="labelContentTemplate"
					[textAreaTemplate]="inputContentTemplate">
				</cds-textarea-label>
			</ng-container>
			<ng-container *ngSwitchCase="'TextInput'">
				<cds-text-label
					[labelInputID]="labelInputID"
					[disabled]="disabled"
					[skeleton]="skeleton"
					[helperText]="helperText"
					[invalid]="invalid"
					[invalidText]="invalidText"
					[warn]="warn"
					[warnText]="warnText"
					[ariaLabel]="ariaLabel"
					[labelTemplate]="labelContentTemplate"
					[textInputTemplate]="inputContentTemplate">
				</cds-text-label>
			</ng-container>
			<ng-container *ngSwitchCase="'PasswordInput'">
				<cds-password-label
					[labelInputID]="labelInputID"
					[disabled]="disabled"
					[skeleton]="skeleton"
					[helperText]="helperText"
					[invalid]="invalid"
					[invalidText]="invalidText"
					[warn]="warn"
					[warnText]="warnText"
					[ariaLabel]="ariaLabel"
					[labelTemplate]="labelContentTemplate"
					[passwordInputTemplate]="inputContentTemplate">
				</cds-password-label>
			</ng-container>
			<ng-container *ngSwitchDefault>
				<ng-template [ngTemplateOutlet]="default"></ng-template>
			</ng-container>
		</ng-container>

		<ng-template #default>
			<label
				[for]="labelInputID"
				[attr.aria-label]="ariaLabel"
				class="cds--label"
				[ngClass]="{
					'cds--label--disabled': disabled,
					'cds--skeleton': skeleton
				}">
				<ng-template [ngTemplateOutlet]="labelContentTemplate"></ng-template>
			</label>
			<div
				class="cds--text-input__field-wrapper"
				[ngClass]="{
					'cds--text-input__field-wrapper--warning': warn
				}"
				[attr.data-invalid]="(invalid ? true : null)"
				#wrapper>
				<svg
					*ngIf="invalid"
					cdsIcon="warning--filled"
					size="16"
					class="cds--text-input__invalid-icon">
				</svg>
				<svg
					*ngIf="!invalid && warn"
					cdsIcon="warning--alt--filled"
					size="16"
					class="cds--text-input__invalid-icon cds--text-input__invalid-icon--warning">
				</svg>
				<ng-template [ngTemplateOutlet]="inputContentTemplate"></ng-template>
			</div>
			<div
				*ngIf="!skeleton && helperText && !invalid && !warn"
				class="cds--form__helper-text"
				[ngClass]="{'cds--form__helper-text--disabled': disabled}">
				<ng-container *ngIf="!isTemplate(helperText)">{{helperText}}</ng-container>
				<ng-template *ngIf="isTemplate(helperText)" [ngTemplateOutlet]="helperText"></ng-template>
			</div>
			<div *ngIf="invalid" class="cds--form-requirement">
				<ng-container *ngIf="!isTemplate(invalidText)">{{invalidText}}</ng-container>
				<ng-template *ngIf="isTemplate(invalidText)" [ngTemplateOutlet]="invalidText"></ng-template>
			</div>
			<div *ngIf="!invalid && warn" class="cds--form-requirement">
				<ng-container *ngIf="!isTemplate(warnText)">{{warnText}}</ng-container>
				<ng-template *ngIf="isTemplate(warnText)" [ngTemplateOutlet]="warnText"></ng-template>
			</div>
		</ng-template>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    labelInputID: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    skeleton: [{
      type: Input
    }],
    helperText: [{
      type: Input
    }],
    invalidText: [{
      type: Input
    }],
    invalid: [{
      type: Input
    }],
    warn: [{
      type: Input
    }],
    warnText: [{
      type: Input
    }],
    ariaLabel: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["wrapper"]
    }],
    textArea: [{
      type: ContentChild,
      args: [TextArea]
    }],
    textInput: [{
      type: ContentChild,
      args: [TextInput, {
        static: false
      }]
    }],
    passwordInput: [{
      type: ContentChild,
      args: [PasswordInput, {
        static: false
      }]
    }],
    labelClass: [{
      type: HostBinding,
      args: ["class.cds--form-item"]
    }]
  });
})();
var InputModule = class {
};
InputModule.ɵfac = function InputModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || InputModule)();
};
InputModule.ɵmod = ɵɵdefineNgModule({
  type: InputModule,
  declarations: [Label, TextInput, TextArea, PasswordInput, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent],
  imports: [CommonModule, FormsModule, IconModule, ButtonModule, TooltipModule],
  exports: [Label, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent, TextInput, TextArea, PasswordInput]
});
InputModule.ɵinj = ɵɵdefineInjector({
  imports: [CommonModule, FormsModule, IconModule, ButtonModule, TooltipModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InputModule, [{
    type: NgModule,
    args: [{
      declarations: [Label, TextInput, TextArea, PasswordInput, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent],
      exports: [Label, TextareaLabelComponent, TextInputLabelComponent, PasswordInputLabelComponent, TextInput, TextArea, PasswordInput],
      imports: [CommonModule, FormsModule, IconModule, ButtonModule, TooltipModule]
    }]
  }], null, null);
})();
export {
  InputModule,
  Label,
  PasswordInput,
  PasswordInputLabelComponent,
  TextArea,
  TextInput,
  TextInputLabelComponent,
  TextareaLabelComponent
};
//# sourceMappingURL=carbon-components-angular_input.js.map
