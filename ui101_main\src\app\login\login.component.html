<div class="login-container">
  <div class="login-form-wrapper">
    <h1 class="login-title">Sign In</h1>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-field">
        <cds-text-label
          [invalid]="isEmailInvalid()"
          [invalidText]="getEmailErrorMessage()">
          Email
          <input
            cdsText
            type="email"
            formControlName="email"
            placeholder="Enter your email"
            [invalid]="isEmailInvalid()"
            autocomplete="email">
        </cds-text-label>
      </div>

      <div class="form-field">
        <cds-password-label
          [invalid]="isPasswordInvalid()"
          [invalidText]="getPasswordErrorMessage()">
          Password
          <input
            cdsPassword
            formControlName="password"
            placeholder="Enter your password"
            [invalid]="isPasswordInvalid()"
            autocomplete="current-password">
        </cds-password-label>
      </div>

      <div class="form-actions">
        <button
          cdsButton="primary"
          type="submit"
          [disabled]="isSubmitting()"
          class="login-button">
          {{ isSubmitting() ? 'Signing In...' : 'Sign In' }}
        </button>
      </div>
    </form>
  </div>
</div>
