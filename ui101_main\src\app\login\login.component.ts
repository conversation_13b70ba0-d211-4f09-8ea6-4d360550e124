import { Component, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

// Carbon Design System imports
import { InputModule } from 'carbon-components-angular/input';
import { ButtonModule } from 'carbon-components-angular/button';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputModule,
    ButtonModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  protected readonly loginForm: FormGroup;
  protected readonly isSubmitting = signal(false);

  constructor(private formBuilder: FormBuilder) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [
        Validators.required,
        Validators.minLength(6),
        this.passwordValidator
      ]]
    });
  }

  private passwordValidator(control: any) {
    const value = control.value;
    if (!value) {
      return null;
    }

    const hasNumber = /[0-9]/.test(value);
    const hasUpper = /[A-Z]/.test(value);
    const hasLower = /[a-z]/.test(value);

    const valid = hasNumber && hasUpper && hasLower;

    if (!valid) {
      return { passwordStrength: true };
    }

    return null;
  }

  protected onSubmit(): void {
    if (this.loginForm.valid) {
      this.isSubmitting.set(true);
      
      // Simulate login process
      console.log('Login form submitted:', this.loginForm.value);
      
      // Reset submitting state after a delay (simulate API call)
      setTimeout(() => {
        this.isSubmitting.set(false);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      this.loginForm.markAllAsTouched();
    }
  }

  protected getEmailErrorMessage(): string {
    const emailControl = this.loginForm.get('email');
    if (emailControl?.hasError('required')) {
      return 'Email is required';
    }
    if (emailControl?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    return '';
  }

  protected getPasswordErrorMessage(): string {
    const passwordControl = this.loginForm.get('password');
    if (passwordControl?.hasError('required')) {
      return 'Password is required';
    }
    if (passwordControl?.hasError('minlength')) {
      return 'Password must be at least 6 characters long';
    }
    if (passwordControl?.hasError('passwordStrength')) {
      return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }
    return '';
  }

  protected isEmailInvalid(): boolean {
    const emailControl = this.loginForm.get('email');
    return !!(emailControl?.invalid && (emailControl?.dirty || emailControl?.touched));
  }

  protected isPasswordInvalid(): boolean {
    const passwordControl = this.loginForm.get('password');
    return !!(passwordControl?.invalid && (passwordControl?.dirty || passwordControl?.touched));
  }
}
